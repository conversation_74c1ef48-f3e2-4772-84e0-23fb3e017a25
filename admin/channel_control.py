from fastapi import APIRouter, Depends
from pydantic import BaseModel, Field
from typing import List, Optional
from decimal import Decimal

from admin.auth import is_op_user
from services import recharge_channel_service
from persistence.models.models import RechargeChannelEnum, RechargeChannelControl, RechargeChannel<PERSON>hitelist
from utils import response_util

channel_control_router = APIRouter(prefix="/channel_control", tags=["渠道控制管理"])

class ChannelControlRequest(BaseModel):
    """渠道控制配置请求"""
    channel: str = Field(..., description="充值渠道")
    pay_type: str = Field(..., description="支付类型: alipay, wechat")
    min_amount: int = Field(default=0, description="最小金额(元)")
    max_amount: int = Field(default=0, description="最大金额(元)")
    enabled: bool = Field(default=True, description="是否启用")
    ratio: int = Field(default=100, description="比例")
    remark: str = Field(default="", description="备注")


class ChannelControlResponse(BaseModel):
    """渠道控制配置响应"""
    id: int
    channel: str
    channel_description: str
    pay_type: str
    min_amount: int
    max_amount: int
    min_amount_yuan: str  # 元为单位显示
    max_amount_yuan: str  # 元为单位显示
    enabled: bool
    ratio: int
    remark: str
    created_at: str
    updated_at: str

    @classmethod
    def from_model(cls, control: RechargeChannelControl) -> "ChannelControlResponse":
        return cls(
            id=control.id,
            channel=control.channel.value,
            channel_description=control.channel.description,
            pay_type=control.pay_type,
            min_amount=control.min_amount,
            max_amount=control.max_amount,
            min_amount_yuan=f"{control.min_amount / 100:.2f}" if control.min_amount > 0 else "无限制",
            max_amount_yuan=f"{control.max_amount / 100:.2f}" if control.max_amount > 0 else "无限制",
            enabled=control.enabled,
            ratio=control.ratio,
            remark=control.remark,
            created_at=control.created_at.strftime("%Y-%m-%d %H:%M:%S"),
            updated_at=control.updated_at.strftime("%Y-%m-%d %H:%M:%S")
        )

@channel_control_router.get("/channels")
async def get_channels(user=Depends(is_op_user)):
    channels = [channel.value for channel in recharge_channel_service.all_channels]
    return response_util.success({
        "channels": channels,
        "total": len(channels)
    })

@channel_control_router.get("/list")
async def get_channel_controls(user=Depends(is_op_user)):
    """获取所有渠道控制配置"""
    try:
        controls = await recharge_channel_service.get_channel_controls()
        control_list = [ChannelControlResponse.from_model(control) for control in controls]
        
        return response_util.success({
            "controls": control_list,
            "total": len(control_list)
        })
    except Exception as e:
        return response_util.error(500, f"获取渠道控制配置失败: {str(e)}")


@channel_control_router.post("/add")
async def add_channel_control(req: ChannelControlRequest, user=Depends(is_op_user)) :
    """添加渠道控制配置"""
    try:
        # 验证渠道枚举值
        try:
            channel_enum = RechargeChannelEnum(req.channel)
        except ValueError:
            return response_util.error(400, f"无效的渠道: {req.channel}")
        
        # 创建控制配置
        control = RechargeChannelControl(
            channel=channel_enum,
            pay_type=req.pay_type,
            min_amount=req.min_amount,
            max_amount=req.max_amount,
            enabled=req.enabled,
            ratio=req.ratio,
            remark=req.remark
        )
        
        saved_control = await recharge_channel_service.add_channel_control(control)
        await recharge_channel_service.generate_channel_queue()
        return response_util.success({
            "control": ChannelControlResponse.from_model(saved_control),
            "message": "渠道控制配置添加成功"
        })
    except Exception as e:
        return response_util.error(500, f"添加渠道控制配置失败: {str(e)}")


@channel_control_router.post("/update/{control_id}")
async def update_channel_control(control_id: int, req: ChannelControlRequest, user=Depends(is_op_user)) :
    """更新渠道控制配置"""
    try:
        # 获取现有配置
        control = await recharge_channel_service.get_channel_control_by_id(control_id)
        if not control:
            return response_util.error(404, "渠道控制配置不存在")
        
        # 验证渠道枚举值
        try:
            channel_enum = RechargeChannelEnum(req.channel)
        except ValueError:
            return response_util.error(400, f"无效的渠道: {req.channel}")
        
        # 更新配置
        control.channel = channel_enum
        control.pay_type = req.pay_type
        control.min_amount = req.min_amount
        control.max_amount = req.max_amount
        control.enabled = req.enabled
        control.ratio = req.ratio
        control.remark = req.remark
        
        updated_control = await recharge_channel_service.update_channel_control(control)
        await recharge_channel_service.generate_channel_queue()

        return response_util.success({
            "control": ChannelControlResponse.from_model(updated_control),
            "message": "渠道控制配置更新成功"
        })
    except Exception as e:
        return response_util.error(500, f"更新渠道控制配置失败: {str(e)}")


@channel_control_router.delete("/delete/{control_id}")
async def delete_channel_control(control_id: int, user=Depends(is_op_user)):
    """删除渠道控制配置"""
    try:
        success = await recharge_channel_service.delete_channel_control(control_id)
        if success:
            return response_util.success({"message": "渠道控制配置删除成功"})
        else:
            return response_util.error(404, "渠道控制配置不存在")
    except Exception as e:
        return response_util.error(500, f"删除渠道控制配置失败: {str(e)}")


@channel_control_router.post("/batch_enable")
async def batch_enable_channels(channel_names: List[str], user=Depends(is_op_user)):
    """批量启用渠道"""
    try:
        success_count = 0
        for channel_name in channel_names:
            try:
                channel_enum = RechargeChannelEnum(channel_name)
                controls = await RechargeChannelControl.filter(channel=channel_enum).all()
                for control in controls:
                    control.enabled = True
                    await control.save()
                    success_count += 1
            except ValueError:
                continue
        
        return response_util.success({
            "message": f"批量启用成功，共启用 {success_count} 个配置"
        })
    except Exception as e:
        return response_util.error(500, f"批量启用失败: {str(e)}")


@channel_control_router.post("/batch_disable")
async def batch_disable_channels(channel_names: List[str], user=Depends(is_op_user)):
    """批量禁用渠道"""
    try:
        success_count = 0
        for channel_name in channel_names:
            try:
                channel_enum = RechargeChannelEnum(channel_name)
                controls = await RechargeChannelControl.filter(channel=channel_enum).all()
                for control in controls:
                    control.enabled = False
                    await control.save()
                    success_count += 1
            except ValueError:
                continue
        
        return response_util.success({
            "message": f"批量禁用成功，共禁用 {success_count} 个配置"
        })
    except Exception as e:
        return response_util.error(500, f"批量禁用失败: {str(e)}")


@channel_control_router.post("/initialize_defaults")
async def initialize_default_controls(user=Depends(is_op_user)) :
    """初始化默认渠道控制配置"""
    try:
        await recharge_channel_service.initialize_default_channel_controls()
        return response_util.success({"message": "默认渠道控制配置初始化成功"})
    except Exception as e:
        return response_util.error(500, f"初始化失败: {str(e)}")

@channel_control_router.get("/channels")
async def get_available_channels(user=Depends(is_op_user)):
    """获取可用的渠道列表"""
    try:
        channels = []
        for channel in RechargeChannelEnum.user_recharge_channels():
            channels.append({
                "value": channel.value,
                "description": channel.description
            })
        
        pay_types = [
            {"value": "alipay", "description": "支付宝"},
            {"value": "wechat", "description": "微信支付"},
            {"value": "ALL", "description": "所有类型"}
        ]
        
        return response_util.success({
            "channels": channels,
            "pay_types": pay_types
        })
    except Exception as e:
        return response_util.error(500, f"获取渠道列表失败: {str(e)}")


@channel_control_router.get("/test/{channel}/{pay_type}/{amount}")
async def test_channel_enabled(channel: str, pay_type: str, amount: int, user=Depends(is_op_user)) :
    """测试指定渠道、支付类型、金额是否启用"""
    try:
        channel_enum = RechargeChannelEnum(channel)
        enabled = await recharge_channel_service.is_channel_enabled_for_request(channel_enum, pay_type, amount)

        return response_util.success({
            "channel": channel,
            "pay_type": pay_type,
            "amount": amount,
            "amount_yuan": f"{amount / 100:.2f}",
            "enabled": enabled,
            "message": f"渠道 {channel} 对于 {pay_type} 支付 {amount/100:.2f}元 {'启用' if enabled else '禁用'}"
        })
    except ValueError:
        return response_util.error(400, f"无效的渠道: {channel}")
    except Exception as e:
        return response_util.error(500, f"测试失败: {str(e)}")


@channel_control_router.get("/stats")
async def get_channel_stats(pay_type: Optional[str] = None, user=Depends(is_op_user)):
    """获取渠道成功率统计"""
    try:
        from persistence.models.models import RechargeChannelStats

        if pay_type:
            stats = await RechargeChannelStats.filter(pay_type=pay_type).all()
        else:
            stats = await RechargeChannelStats.all()

        stats_list = []
        for stat in stats:
            stats_list.append({
                "channel": stat.channel.value,
                "channel_description": stat.channel.description,
                "pay_type": stat.pay_type,
                "total_orders": stat.total_orders,
                "success_orders": stat.success_orders,
                "success_rate": round(stat.success_rate, 2),
                "last_updated": stat.last_updated.strftime("%Y-%m-%d %H:%M:%S")
            })

        return response_util.success({
            "stats": stats_list,
            "total": len(stats_list),
            "filter_pay_type": pay_type
        })
    except Exception as e:
        return response_util.error(500, f"获取统计数据失败: {str(e)}")


@channel_control_router.post("/stats/update/{channel}")
async def update_channel_stats(channel: str, user=Depends(is_op_user)):
    """手动更新指定渠道的统计数据"""
    try:
        channel_enum = RechargeChannelEnum(channel)
        updated_stats = await recharge_channel_service.update_channel_success_rate(channel_enum)

        stats_list = []
        for stat in updated_stats:
            stats_list.append({
                "channel": stat.channel.value,
                "pay_type": stat.pay_type,
                "total_orders": stat.total_orders,
                "success_orders": stat.success_orders,
                "success_rate": round(stat.success_rate, 2)
            })

        return response_util.success({
            "stats": stats_list,
            "message": f"渠道 {channel} 统计数据更新成功"
        })
    except ValueError:
        return response_util.error(400, f"无效的渠道: {channel}")
    except Exception as e:
        return response_util.error(500, f"更新统计数据失败: {str(e)}")


@channel_control_router.get("/routing/preview/{pay_type}/{amount}")
async def preview_routing(pay_type: str, amount: int, user_type: str = 'type1', user=Depends(is_op_user)):
    """预览指定支付类型和金额的路由结果"""
    try:
        primary_queue, secondary_queue = await recharge_channel_service.get_channel_queues_for_routing(
            user_type, pay_type, amount)

        return response_util.success({
            "user_type": user_type,
            "pay_type": pay_type,
            "amount": amount,
            "amount_yuan": f"{amount / 100:.2f}",
            "primary_queue": primary_queue,
            "secondary_queue": secondary_queue,
            "message": f"{user_type} 用户使用 {pay_type} 支付 {amount/100:.2f}元 的路由预览"
        })
    except Exception as e:
        return response_util.error(500, f"路由预览失败: {str(e)}")

class WhitelistRequest(BaseModel):
    """白名单请求"""
    tg_id: int = Field(..., description="Telegram用户ID")
    channel: str = Field(..., description="充值渠道")
    remark: str = Field(default="", description="备注")

class WhitelistResponse(BaseModel):
    id: int
    tg_id: int
    channel: str
    channel_description: str
    enabled: bool
    remark: str
    created_at: str
    updated_at: str

    @classmethod
    def from_model(cls, whitelist: RechargeChannelWhitelist) -> "WhitelistResponse":
        return cls(
            id=whitelist.id,
            tg_id=whitelist.tg_id,
            channel=whitelist.channel.value,
            channel_description=whitelist.channel.description,
            enabled=whitelist.enabled,
            remark=whitelist.remark,
            created_at=whitelist.created_at.strftime("%Y-%m-%d %H:%M:%S"),
            updated_at=whitelist.updated_at.strftime("%Y-%m-%d %H:%M:%S")
        )

@channel_control_router.get("/whitelist")
async def get_whitelist(user=Depends(is_op_user)):
    try:
        whitelist_entries = await recharge_channel_service.get_whitelist_entries()
        whitelist_list = [WhitelistResponse.from_model(entry) for entry in whitelist_entries]

        return response_util.success({
            "whitelist": whitelist_list,
            "total": len(whitelist_list)
        })
    except Exception as e:
        return response_util.error(500, f"获取白名单失败: {str(e)}")


@channel_control_router.post("/whitelist/add")
async def add_whitelist(req: WhitelistRequest, user=Depends(is_op_user)):
    """添加白名单条目"""
    try:
        try:
            channel_enum = RechargeChannelEnum(req.channel)
        except ValueError:
            return response_util.error(400, f"无效的渠道: {req.channel}")

        entry = await recharge_channel_service.add_whitelist_entry(
            req.tg_id, channel_enum, req.remark
        )

        return response_util.success({
            "whitelist": WhitelistResponse.from_model(entry),
            "message": "白名单添加成功"
        })
    except Exception as e:
        return response_util.error(500, f"添加白名单失败: {str(e)}")


@channel_control_router.post("/whitelist/delete/{tg_id}/{channel}")
async def remove_whitelist(tg_id: int, channel: str, user=Depends(is_op_user)):
    try:
        try:
            channel_enum = RechargeChannelEnum(channel)
        except ValueError:
            return response_util.error(400, f"无效的渠道: {channel}")

        success = await recharge_channel_service.remove_whitelist_entry(tg_id, channel_enum)
        if success:
            return response_util.success({"message": "白名单移除成功"})
        else:
            return response_util.error(404, "白名单条目不存在")
    except Exception as e:
        return response_util.error(500, f"移除白名单失败: {str(e)}")

@channel_control_router.post("/whitelist/update/{entry_id}")
async def update_whitelist(entry_id: int, enabled: bool, remark: str = "", user=Depends(is_op_user)):
    try:
        entry = await recharge_channel_service.update_whitelist_entry(entry_id, enabled, remark)

        return response_util.success({
            "whitelist": WhitelistResponse.from_model(entry),
            "message": "白名单更新成功"
        })
    except Exception as e:
        return response_util.error(500, f"更新白名单失败: {str(e)}")

@channel_control_router.get("/whitelist/check/{tg_id}/{channel}")
async def check_whitelist(tg_id: int, channel: str, user=Depends(is_op_user)):
    try:
        try:
            channel_enum = RechargeChannelEnum(channel)
        except ValueError:
            return response_util.error(400, f"无效的渠道: {channel}")

        is_whitelisted = await recharge_channel_service.get_user_whitelist_channel(tg_id)

        return response_util.success({
            "tg_id": tg_id,
            "channel": channel,
            "is_whitelisted": is_whitelisted,
            "message": f"用户 {tg_id} 在渠道 {channel} {'在' if is_whitelisted else '不在'}白名单中"
        })
    except Exception as e:
        return response_util.error(500, f"检查白名单失败: {str(e)}")

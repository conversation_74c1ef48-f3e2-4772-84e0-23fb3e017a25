from decimal import Decimal
import uuid
from fastapi import Depends
from fastapi.routing import APIRouter
from pydantic import BaseModel, computed_field

from admin.auth import is_op_user
from common.common_constant import RpDisplayFilter
from services import jlbzf_recharge_service, recharge_channel_service, recharge_service, sjzf_recharge_service, star_payment_service, bot_services, sdfkw_recharge_service, tmpay_recharge_service, ff_recharge_service, qszf_recharge_service
from utils import response_util
from persistence.models.models import RechargeChannelConfig, RechargeChannelEnum, RechargeOrder
from controllers.recharge_handler import RechargeHandlerFactory

class StarCompensateRequest(BaseModel):
    user_tg_id: int
    bot_id: int

class GenerateRechargeLinkRequest(BaseModel):
    channel: str
    type: str
    recharge_id: str

recharge_router = APIRouter(dependencies=[Depends(is_op_user)])

@recharge_router.get('/order_by_id')
async def get_order_by_id(order_id: str):
    order = await recharge_service.get_order_by_id(order_id)
    if order is None:
        return response_util.error(message="Order not found", error_code=404)
    return response_util.ok(data={"order": order})

@recharge_router.get('/order_by_out_order_id')
async def get_order_by_out_order_id(out_order_id: str):
    order = await recharge_service.get_order_by_out_order_id(out_order_id)
    if order is None:
        return response_util.error(message="Order not found", error_code=404)
    return response_util.ok(data={"order": order})

@recharge_router.post('/star_compensate')
async def star_compensate(request: StarCompensateRequest):
    user_id = request.user_tg_id
    bot = bot_services.get_bot_by_bot_id(request.bot_id)
    if bot is None:
        return response_util.error(message="Bot not found", error_code=404)
    transactions = await bot.get_star_transactions()
    used_transaction = []
    for transaction in transactions.transactions:
        if not transaction.source:
            continue
        if transaction.source.user.id == user_id:
            await star_payment_service.star_compensate(transaction)
            used_transaction.append(transaction)

    return response_util.ok(message="Compensation finished.", data={"transactions": used_transaction})

def get_channels():
    alipay_channels = RechargeChannelEnum.alipay_recharge_channels()
    alipay_channels = [{"name": channel.description, "value": channel} for channel in alipay_channels if channel != RechargeChannelEnum.STAR_PAYMENT.value]
    alipay_channels.append({"name": "默认", "value": "DEFAULT"})
    alipay_channels.append({"name": "关闭", "value": "CLOSED"})

    wechat_channels = RechargeChannelEnum.wechat_recharge_channels()
    wechat_channels = [{"name": channel.description, "value": channel} for channel in wechat_channels if channel != RechargeChannelEnum.STAR_PAYMENT.value]
    wechat_channels.append({"name": "默认", "value": "DEFAULT"})
    wechat_channels.append({"name": "关闭", "value": "CLOSED"})

    channels = {
        "alipay": alipay_channels,
        "wechat": wechat_channels
    }
    return channels

@recharge_router.get('/supported_channels')
async def get_supported_channels():
    channels = get_channels()
    return response_util.ok(data={"channels": channels})

class ConfigEditRequest(BaseModel):
    recharge_product_id: str
    channels: dict[str, str]

class ConfigDto(BaseModel):
    recharge_product_id: str
    recharge_product_name: str = ''
    cny_price: str = ''
    wechat_channel: str = 'DEFAULT'
    alipay_channel: str = 'DEFAULT'
    voucher_channel: str = 'DEFAULT'
    star_channel: str = 'DEFAULT'
    usdt_channel: str = 'DEFAULT'

    @computed_field
    @property
    def channel_list(self) -> list:
        return [
            {"wechat": self.wechat_channel},
            {"alipay": self.alipay_channel},
            {"voucher": self.voucher_channel},
            {"star": self.star_channel},
            {"usdt": self.usdt_channel}
        ]
    

@recharge_router.get('/recharge_configs')
async def get_recharge_configs():
    products = await recharge_service.get_recharge_products()
    recharge_configs = await recharge_channel_service.get_configs()
    configs = {str(config.recharge_product_id): config for config in recharge_configs}
    result = []
    for product in products:
        if product.display_filters and RpDisplayFilter.IMAGE_BOT_ONLY.value in product.display_filters:
            continue
        config_dto = ConfigDto(
            recharge_product_id=str(product.recharge_product_id),
            recharge_product_name=product.title,
            cny_price=f'{(Decimal(product.cny_price) / 1000 / 100):.2f}',
        )
        config = configs.get(str(product.recharge_product_id))
        if config:
            config_dto.wechat_channel = config.wechat_channel
            config_dto.alipay_channel = config.alipay_channel
            config_dto.voucher_channel = config.voucher_channel
            config_dto.star_channel = config.star_channel
            config_dto.usdt_channel = config.usdt_channel
        result.append(config_dto)

    channels = get_channels()
    return response_util.ok(data={"recharge_configs": result, "channels": channels})

@recharge_router.post('/recharge_config/edit')
async def add_recharge_config(req: ConfigEditRequest):
    config = await recharge_channel_service.get_by_product_id(req.recharge_product_id)
    if config is None:
        config_model = RechargeChannelConfig(
            recharge_product_id=req.recharge_product_id,
            wechat_channel=req.channels.get('wechat', 'DEFAULT'),
            alipay_channel=req.channels.get('alipay', 'DEFAULT'),
            voucher_channel=req.channels.get('voucher', 'DEFAULT'),
            star_channel=req.channels.get('star', 'DEFAULT'),
            usdt_channel=req.channels.get('usdt', 'DEFAULT'),
        )
        await recharge_channel_service.add_config(config_model)
    else:
        config.wechat_channel = req.channels.get('wechat', 'DEFAULT')
        config.alipay_channel = req.channels.get('alipay', 'DEFAULT')
        config.voucher_channel = req.channels.get('voucher', 'DEFAULT')
        config.star_channel = req.channels.get('star', 'DEFAULT')
        config.usdt_channel = req.channels.get('usdt', 'DEFAULT')
        await recharge_channel_service.update_config(config)

    new_config = await recharge_channel_service.get_by_product_id(req.recharge_product_id)

    return response_util.ok(message="Config updated successfully.", data={"config": new_config})

@recharge_router.post('/recharge_config/generate_link')
async def generate_recharge_link(req: GenerateRechargeLinkRequest):
    if req.type == 'wxpay':
        req.type = 'wechat'
    recharge_product = await recharge_service.get_recharge_product(req.recharge_id)
    order = RechargeOrder(
        recharge_order_id=uuid.uuid4(),
        pay_fee=recharge_product.cny_price,
    )
    host = '127.0.0.1'
    handler = RechargeHandlerFactory.get_handler(req.channel)

    result = await handler._process_payment(order, req.type, host, True)
    if result.success:
        return response_util.ok(data={"link": result.pay_url})
    else:
        return response_util.error(error_code=result.status_code or 400, message=result.message)
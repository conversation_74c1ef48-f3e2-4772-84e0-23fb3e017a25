import logging
from random import Random, random
import re
import unicodedata

from common.common_constant import Language, PresetReplace

log = logging.getLogger(__name__)


def format_char_and_user(content: str, role_name: str, user_name: str) -> str:
    if is_empty(content):
        return content
    content = format_user(content, user_name)
    content = format_char(content, role_name)
    return content


def format_user(content: str, user_name: str) -> str:
    if is_empty(content):
        return content
    if user_name is None or user_name.strip() == "":
        return content
    return content.replace("{{user}}", user_name)


def format_char(content: str, char_name: str) -> str:
    if is_empty(content):
        return content
    return content.replace("{{char}}", char_name)


def format_status_bar(content: str, status_bar: str) -> str:
    if is_empty(content):
        return content
    return content.replace("{{StatusBar}}", status_bar)


def format_lastmessage(content: str, last_message_id: str, last_message: str) -> str:
    return content.replace("{{lastMessageId}}", str(last_message_id)).replace(
        "{{lastMessage}}", last_message
    )


def format_random(content: str) -> str:
    if "{{random" not in content:
        return content
    random_pattern = re.compile(r"{{random\s?::?([^}]+)}}", re.IGNORECASE)

    def replace_match(match):
        list_string = match.group(1)
        # Split on either double colons or comma. If comma is the separator, we are also trimming all items.
        if "::" in list_string:
            list_items = list_string.split("::")
        else:
            # Replace escaped commas with a placeholder to avoid splitting on them
            list_string_with_placeholders = list_string.replace("\\,", "##123##")
            list_items = [
                item.strip().replace("##123##", ",")
                for item in list_string_with_placeholders.split(",")
            ]

        if len(list_items) == 0:
            return ""
        rng = Random()
        random_index = rng.randint(0, len(list_items) - 1)
        return list_items[random_index]

    input_string = re.sub(random_pattern, replace_match, content)
    return input_string


def is_not_empty_content(x: dict, k: str) -> bool:
    empty = k not in x or x[k] == None or x[k].strip() == ""
    return not empty


def is_empty_content(x: dict, k: str) -> bool:
    return not is_not_empty_content(x, k)


def is_empty(x: str) -> bool:
    return x == None or x.strip() == ""


def is_not_empty(k: str) -> bool:
    return k != None and k.strip() != ""


def remove_user_placeholder(content: str) -> str:
    ret = content.replace("{{user}}", "")
    if ret.startswith("：") or ret.startswith(":"):
        ret = ret[1:]
    return ret


def remove_char_placeholder(content: str) -> str:
    ret = content.replace("{{char}}", "")
    if ret.startswith("：") or ret.startswith(":"):
        ret = ret[1:]
    return ret


def format_personality(
    personality_format: str, data_config: dict, user_name: str, role_name: str
) -> str:
    if is_empty_content(data_config, "personality"):
        return ""
    content = data_config["personality"]
    if is_not_empty(personality_format):
        content = personality_format.replace("{{personality}}", content)
    content = format_status_bar_with_config(data_config, content)
    content = format_user(content, user_name)
    content = format_char(content, role_name)
    return content


def format_scenario(
    data_config: dict,
    scenario_format: str,
    content: str,
    user_name: str,
    role_name: str,
) -> str:
    if is_empty(content):
        return ""
    if is_not_empty(scenario_format):
        content = scenario_format.replace("{{scenario}}", content)
    content = format_status_bar_with_config(data_config, content)
    content = format_user(content, user_name)
    content = format_char(content, role_name)
    return content


def format_description(data_config: dict, user_name: str, role_name: str) -> str:
    if is_empty_content(data_config, "description"):
        return ""

    content = data_config["description"]
    if is_not_empty(content):
        content = format_status_bar_with_config(data_config, content)
        content = format_user(content, user_name)
        content = format_char(content, role_name)
    return content


def format_status_bar_with_config(data_config: dict, content: str) -> str:
    if not data_config.get("statusBlockEnable", False):
        return content.replace("{{StatusBar}}", "")

    status_block = format_status_block(
        data_config.get("status_block", ""), data_config.get("statusBlockType", "")
    )
    content = format_status_bar(content, status_block)
    return format_status_block_rule(data_config, content)


def format_status_block(
    status_block: str, block_type: str, contain_tag: bool = False
) -> str:
    if status_block.strip() == "":
        return ""
    ret = ""
    if block_type == "normal":
        ret = f"```\n{status_block}\n```"
    elif block_type == "collapse":
        ret = f"<details>\n\n```\n{status_block}\n```\n</details>"
    else:
        ret = f"<!--\n```\n{status_block}\n```\n-->"
    if ret and contain_tag:
        ret = f"<StatusBlock>\n{ret}\n</StatusBlock>"
    return ret


def format_status_block_rule(data_config: dict, content: str) -> str:
    rule = data_config.get("status_block_rule", "")
    return content.replace("{{StatusRules}}", rule)


def format_placeholder(content: str, placeholder: str, target: str) -> str:
    if placeholder not in content:
        return content
    if not target:
        target = ""
    return content.replace(placeholder, target)


def format_hot(hot: int) -> str:
    if hot < 10000:
        return str(hot)
    return f"{hot / 10000:.1f}w"


# 判断是否是乱码
def garbled_code_list(content: str) -> list[str]:
    # 定义不可读字符集合
    UNREADABLE_CHARS = {
        "\ufffd",  # 替换字符
        "\u0000",  # NULL字符
    }

    # 定义可读字符的Unicode范围
    READABLE_RANGES = [
        (0, 127),  # ASCII字符
        (0x4E00, 0x9FFF),  # 汉字
        (0xFF00, 0xFF65),  # 常见的全角字符
        (0x3040, 0x30FF),  # 日文假名
        (0xAC00, 0xD7AF),  # 韩文
        (0x2200, 0x22FF),  # 数学符号
    ]

    def is_readable(char):
        """
        检查字符是否可读
        返回 True 如果字符是可读的，否则返回 False
        """
        try:
            if not char:
                return True
            if char in UNREADABLE_CHARS:
                return False
            # 获取字符的Unicode类别
            category = unicodedata.category(char)
            # 检查是否是控制字符
            if category.startswith("C"):
                return True
            # 检查标点符号
            if category.startswith("P"):
                return True
            # 检查字符是否在可读范围内
            code_point = ord(char)
            for start, end in READABLE_RANGES:
                if start <= code_point <= end:
                    return True
            # 检查emoji和特殊符号
            if category.startswith(("So", "Sm")) or re.match(
                r"[\U0001F300-\U0001F9FF]", char
            ):
                return True
            try:
                unicodedata.name(char)
                return True
            except ValueError:
                return False
        except Exception:
            return False

    ret_list = [char for char in content if not is_readable(char)]
    return ret_list


def garbled_code_len(content: str) -> int:
    return len(garbled_code_list(content))


def format_avatar(avatar: str) -> str:
    if not avatar:
        return avatar
    if "sgp-ai-data-1323765209.cos.ap-singapore.myqcloud.com" in avatar:
        avatar = avatar.replace(
            "https://sgp-ai-data-1323765209.cos.ap-singapore.myqcloud.com/", ""
        )
        avatar = avatar.split("?")[0]
        avatar = f"https://ai-data.424224.xyz/{avatar}"
    elif "tr-avatar-1323765209.cos.ap-singapore.myqcloud.com" in avatar:
        avatar = avatar.replace(
            "https://tr-avatar-1323765209.cos.ap-singapore.myqcloud.com/", ""
        )
        avatar = avatar.split("?")[0]
        avatar = f"https://ai-data.424224.xyz/image/avatar/{avatar}"
    return avatar


# 模糊图片
def handle_spoiler_avatar(avatar: str):
    if not avatar:
        return avatar
    avatar = format_avatar(avatar)
    if "ai-data.424224.xyz" in avatar:
        return f"https://static.image.424224.xyz/cdn-cgi/image/blur=100/{avatar}"
    elif "myqcloud.com" in avatar:
        if avatar.find("?") >= 0:
            return avatar + "&imageMogr2/blur/100x100"
        return avatar + "?imageMogr2/blur/100x100"
    return avatar


def escape_tg_text(content: str) -> str:
    return (
        content.replace("-", "\\-")
        .replace("(", "\\(")
        .replace(")", "\\)")
        .replace("!", "\\!")
        .replace("_", "\\_")
        .replace(".", "\\.")
        .replace("=", "\\=")
        .replace("+", "\\+")
        .replace("~", "\\~")
        .replace("*", "\\*")
        .replace("#", "\\#")
    )


def escape_tg_chat_text(content: str) -> str:
    return (
        content.replace("(", "\\(")
        .replace(")", "\\)")
        .replace("[", "\\[")
        .replace("]", "\\]")
        .replace("_", "\\_")
        .replace("=", "\\=")
        .replace("+", "\\+")
        .replace("~", "\\~")
        .replace("#", "\\#")
    )


def format_tg_html_text(content: str) -> str:
    return content.replace("<", "&lt;").replace(">", "&gt;").replace("&", "&amp;")


def format_markdown_text(content: str) -> str:
    return (
        content.replace("_", "\\_")
        .replace("*", "\\*")
        .replace("~", "\\~")
        .replace("-", "\\-")
        .replace("(", "\\(")
        .replace(")", "\\)")
    )


# tg caption裁剪
def tg_caption_cut(content: str) -> str:
    if len(content) > 600:
        content = content[:600] + "..."
    return content


def replace_new_status_block(content: str, status_block: str) -> str:
    if not content or not status_block:
        return content
    if "<StatusBlock>" not in content:
        return f"{content}\n{status_block}"
    return re.sub(
        r"<StatusBlock>.*?</StatusBlock>", status_block, content, flags=re.DOTALL
    )


# 解析status block
def parse_status_block(content: str) -> str:
    if not content:
        return ""
    status_block = ""
    if "<StatusBlock>" in content and "</StatusBlock>" in content:
        match = re.search(r"<StatusBlock>(.*?)</StatusBlock>", content, re.DOTALL)
        status_block = match.group(1).strip() if match else ""
    elif "<StatusBlock>" in content and "</StatusBlock>" not in content:
        match = re.search(r"<StatusBlock>(.*?)$", content, re.DOTALL)
        status_block = match.group(1).strip() if match else ""
    if not status_block:
        return ""
    # 去除所有标签、去除开头和结尾格式化符合```，去除开头的空格和换行，
    status_block = status_block.strip()
    status_block = re.sub(r"<[^>]+>", "", status_block).strip()
    if status_block.startswith("```"):
        status_block = status_block[3:].strip()
    if status_block.endswith("```"):
        status_block = status_block[:-3].strip()
    return status_block


SHARE_PROHIBITED_WORDS = [
    "幼女",
    "young girl",
    "loli",
    "萝莉",
    "小学生",
    "初中",
    "未满18岁",
    "未成年",
    "婴儿",
    "人兽",
    "尸体",
]
SHARE_PROHIBITED_WORDS_REG = [
    "0岁",
    "1岁",
    "2岁",
    "3岁",
    "4岁",
    "5岁",
    "6岁",
    "7岁",
    "8岁",
    "9岁",
    "10岁",
    "11岁",
    "12岁",
    "13岁",
    "14岁",
    "15岁",
    "16岁",
    "17岁",
]


def is_prohibited_words(content: str) -> list[str]:
    if not content:
        return []
    ret = [word for word in SHARE_PROHIBITED_WORDS if word in content]
    if ret:
        return ret
    for age in SHARE_PROHIBITED_WORDS_REG:
        # 构建正则表达式
        # (?<!\d) 表示前面不能是数字
        # (?!\d) 表示后面不能是数字
        pattern = f"(?<!\\d){age}(?!\\d)"
        if re.search(pattern, content):
            return [age]
    return []


# 替换用户昵称为占位符
def replace_to_placeholder(content: str, nickname: str, role_name: str) -> str:
    if not content:
        return content
    content = content.replace(nickname, PresetReplace.USER.value)
    content = content.replace(role_name, PresetReplace.CHAR.value)
    return content


def replace_nickname_placeholder(content: str, nickname: str) -> str:
    if not content or not nickname:
        return content
    return content.replace(nickname, PresetReplace.USER.value)


def replace_char_placeholder(content: str, role_name: str) -> str:
    if not content:
        return content
    return content.replace(role_name, PresetReplace.CHAR.value)


def format_web_domain(
    domain: str, language: str = Language.ZH.value
) -> str:
    if not domain:
        return domain
    if domain.endswith("/"):
        domain = domain[:-1]
    return f"{domain}/{language}"

from collections import Counter
from datetime import datetime
import json
import json.tool
import logging
import re
from typing import Annotated, Any, Dict, Optional
from aiogram import Bot
from aiogram.client.session.aiohttp import AiohttpSession
from fastapi import (
    APIRouter,
    Depends,
    Header,
    Request,
    UploadFile,
    File,
    Form,
    requests,
)
from fastapi.responses import JSONResponse
from requests import request

from common.common_constant import (
    ERROR_CODE,
    AuditStatus,
    ChatModeType,
    CosPrefix,
    ErrorCode,
    Language,
    RoleFilterChatType,
    RoleFilterTag,
    RoleGenderType,
    RolePlayType,
    RoleSortType,
    RoleTag,
    UploadImageCategory,
)
from common.models.common_res_model import SpeakerRes
from common.role_model import (
    CardDetail,
    ChatGroupEdit,
    RoleConfigResponse,
    RoleEditDetail,
    RoleFilterBrief,
    RoleFilterRequest,
    RoleFilterResponse,
    UserChatGroupEdit,
    UserRoleBrief,
)
from common.translate_model import TranslateTaskType
from controllers.request_depends import limit_requests_by_tg
from services import (
    bot_message_service,
    product_service,
    role_access_service,
    search_service,
    tag_service,
    translate_service,
    user_role_service,
)
from services import role_config_service
from services.role import (
    character_book_service,
    role_audit_service,
    role_group_service,
    role_loader_service,
    role_verify_service,
)
from services.user_service import UserService
from services.voice_speaker_service import VoiceSpeakerService
from tasks import init_config_task
from tasks.translate import trans_role_desc_task
from utils import (
    cos_util,
    env_util,
    exception_util,
    image_util,
    json_util,
    object_util,
    response_util,
    role_util,
    token_util,
)
from utils.translate_util import _tl
from .user_check import get_current_user, get_user_id_un_verify

from services.role_config_service import RoleConfigService
from persistence.models.models import (
    RoleConfig,
)

from controllers import user_check

log = logging.getLogger(__name__)

role_router = APIRouter()

user_service = UserService()


@role_router.get("/health")
async def health():
    return JSONResponse(content={"status": "ok"})


# @role_router.get("/roles/init_role_orders")
# async def init_role_orders(temp: bool = True):
#     orders = await init_config_task.init_role_orders(temp)
#     return response_util.ok({"orders": orders})


@role_router.get("/roles/create_config")
@limit_requests_by_tg("roles:create_config", 5)
async def createConfig(
    request: Request,
    role_id: int = 0,
    user_id: int = Depends(get_current_user),
    current_language: str = Header(default=Language.ZH.value),
) -> RoleConfigResponse:
    USER_UN_DISPLAY_TAGS = ["状态栏", "可发私照"]

    all_tags = await tag_service.list_sub_tags_with_enabled()
    all_tags = [tag for tag in all_tags if tag.tag_name not in USER_UN_DISPLAY_TAGS]
    ret: RoleConfigResponse = await RoleConfigService.get_created_config(
        role_id=role_id
    )
    if ret.role and ret.role.sub_tags:
        sub_tag_names = json_util.convert_to_list(ret.role.sub_tags)
        ret.role.sub_tags = await tag_service.list_sub_tag_names(
            sub_tag_names, current_language
        )
    speaker_list = await VoiceSpeakerService.get_active_speakers()
    speaker_list.sort(key=lambda x: x.order)
    speaker_list = (
        [SpeakerRes.from_model(speaker) for speaker in speaker_list]
        if speaker_list
        else []
    )
    for speaker in speaker_list:
        speaker.speaker_name = _tl(speaker.speaker_name, current_language)
    ret.all_tags = [tag.tag_name for tag in all_tags]
    ret.sub_tag_category_list = await tag_service.list_category_group_sub_tags(
        current_language
    )
    ret.tag_orders = await tag_service.list_tags_with_orders()
    ret.speakers = speaker_list
    ret.chat_products = await product_service.list_display_chat_product(
        current_language
    )
    return ret


@role_router.get("/roles/detail")
async def role_detail(
    request: Request,
    role_id: int,
    current_language: str = Header(default=Language.ZH.value),
):
    user = await get_user_id_un_verify(request)
    role_detail = await user_role_service.get_role_detail(
        user, role_id, current_language
    )
    if not role_detail:
        return response_util.error(ERROR_CODE.PARAM_ERROR.value, "Role not found")
    return role_detail


@role_router.get("/roles/search")
@limit_requests_by_tg("roles:search", 5)
async def search_role(
    request: Request,
    nsfw: bool = True,
    keyword: str = "",
    offset: int = 0,
    user_id: int = Depends(get_current_user),
    current_language: str = Header(default=Language.ZH.value),
    real_role: bool = False,
    eng_bot: str = Header(None),
) -> RoleFilterResponse:
    user = await user_check.get_user_id_un_verify(request)
    nickname = user.nickname if user is not None else ""
    user_id = user.id if user is not None else 0
    total, ids = await search_service.list_roles(
        nsfw, keyword, offset, 15, current_language
    )
    log.info(
        f"search_role,user_id:{user_id},nsfw:{nsfw},keyword:{keyword},total:{total},ids:{ids}"
    )
    search_rets: list[RoleConfig] = await role_loader_service.load_translated_roles(
        ids, current_language, nickname
    )
    if real_role:
        search_rets = [x for x in search_rets if x.real_role]
    ret_roles = [RoleFilterBrief.from_model(x) for x in search_rets]
    response = RoleFilterResponse(
        count=total,
        tags=[],
        sub_tags=[],
        roles=ret_roles,
    )
    # if eng_bot:
    # role_util.anonymous_response_authors(response)
    return response


# 创建群组使用
@role_router.get("/roles/filter_list")
@limit_requests_by_tg("roles:filter_list", 5)
async def filter_role_list(
    request: Request,
    nsfw: bool = True,
    offset: int = 0,
    limit: int = 100,
    current_language: str = Header(default=Language.ZH.value),
    user_id: int = Depends(get_current_user),
) -> RoleFilterResponse:
    ret = await role_config_service.group_create_roles(
        nsfw, offset, limit, current_language
    )
    return ret


# fancyou专用
@role_router.get("/roles/list")
@limit_requests_by_tg("roles:list", 5)
async def roles_list(
    request: Request,
    nsfw: bool = True,
    tag: str = "",
    sub_tag: str = "",
    limit: int = 100,
    offset: int = 0,
    current_language: str = Header(default=Language.ZH.value),
    user_id: int = Depends(get_current_user),
) -> RoleFilterResponse:
    user = await user_check.get_user_id_un_verify(request)
    nickname = user.nickname if user is not None else ""
    tag = tag if tag else RoleTag.CHOSEN.translate(current_language)
    ret = await role_config_service.old_tab_roles(
        nsfw, tag, sub_tag, current_language, nickname, offset, limit
    )
    ret.latest_card_created_at = await role_loader_service.latest_card_created_at()
    return ret


# 获取指定用户的已发布的角色卡和群聊卡(不返回匿名的)
@role_router.get("/roles/published/other")
@limit_requests_by_tg("roles:published:other", 5)
async def get_user_roles(
    request: Request,
    other_user_id: int,
    offset: int = 0,
    limit: int = 10,
    user_id: int = Depends(get_current_user),
    current_language: str = Header(default=Language.ZH.value),
):
    if other_user_id <= 0:
        return response_util.ok({"list": [], "total": 0})
    user = await user_service.get_user_by_id(other_user_id)
    if not user:
        return response_util.ok({"list": [], "total": 0})
    # 获取用户的群聊卡和角色卡（过滤掉匿名的）
    group_id_create_time_list = (
        await role_group_service.list_public_id_create_time_by_user_id(
            other_user_id, True
        )
    )
    role_id_create_time_list = (
        await role_loader_service.list_public_id_create_time_by_user_id(
            other_user_id, True
        )
    )
    all_list = group_id_create_time_list + role_id_create_time_list
    # 按照创建时间排序
    all_list.sort(key=lambda x: x.get("created_at", 0), reverse=True)
    total = len(all_list)
    if offset >= total:
        return response_util.ok({"list": [], "total": total})
    target_list = all_list[offset : offset + limit]

    # group detail list
    target_group_ids = [
        x["mode_target_id"]
        for x in target_list
        if x["mode_type"] == ChatModeType.GROUP.value
    ]
    log.info(f"target_group_ids: size:{len(target_group_ids)},value:{target_group_ids}")
    group_list = await role_group_service.list_translate_detail_by_ids(
        target_group_ids, current_language
    )
    group_card_list = [CardDetail.from_group(x) for x in group_list]

    # role detail list
    target_role_ids = [
        x["mode_target_id"]
        for x in target_list
        if x["mode_type"] == ChatModeType.SINGLE.value
    ]
    log.info(f"target_role_ids: size:{len(target_role_ids)},value:{target_role_ids}")
    ret_roles = await role_loader_service.load_translated_roles(
        target_role_ids, current_language, ""
    )
    ret_roles = [UserRoleBrief.from_config_and_audit(x) for x in ret_roles]
    role_card_list = [CardDetail.from_role(x) for x in ret_roles]

    # add author name
    total_card_list = group_card_list + role_card_list
    await role_config_service.set_nick_name(total_card_list, current_language)

    # result
    group_card_map = {x.mode_target_id: x for x in group_card_list}
    role_card_map = {x.mode_target_id: x for x in role_card_list}
    ret_list = []
    for x in target_list:
        if x["mode_type"] == ChatModeType.GROUP.value:
            item = group_card_map.get(x["mode_target_id"])
            if item:
                ret_list.append(item)
        else:
            item = role_card_map.get(x["mode_target_id"])
            if item:
                ret_list.append(item)
    return response_util.ok({"list": ret_list, "total": total})


# @role_router.get("/roles/filter_list_v1")
# async def filter_role_list_v1(
#     request: Request,
#     nsfw: bool = True,
#     tag: str = "",
#     sub_tags: str = "",
#     product_ids: str = "",
#     limit: int = 10,
#     offset: int = 0,
#     current_language: str = Header(default=Language.ZH.value),
# ):
#     if limit not in [10, 20]:
#         log.error("limit not in [10, 20],limit: %s", limit)
#         raise exception_util.param_error("Param Error")
#     user = await user_check.get_user_id_un_verify(request)
#     nickname = user.nickname if user is not None else ""
#     tag = tag if tag else RoleTag.CHOSEN.translate(current_language)
#     sub_tag_list = sub_tags.split(",") if sub_tags else []
#     product_ids_list = product_ids.split(",") if product_ids else []
#     filter = RoleFilterRequest(
#         nsfw=nsfw,
#         tag=tag,
#         sub_tags=sub_tag_list,
#         product_ids=product_ids_list,
#         language=current_language,
#         offset=offset,
#         limit=limit,
#     )
#     filter_response = await role_config_service.new_tab_roles_v2(filter, nickname)
#     return {
#         "list": filter_response.card_list,
#         "count": filter_response.count,
#         "tags": filter_response.tags,
#         "sub_tags": filter_response.sub_tags,
#         "latest_card_created_at": filter_response.latest_card_created_at,
#         "current_tag": filter_response.current_tag,
#         "current_sub_tag": filter_response.current_sub_tag,
#         "current_sub_tags": filter_response.current_sub_tags,
#     }


@role_router.get("/roles/filter_list_v2")
async def filter_role_list_v2(
    nsfw: bool = True,
    tag: str = RoleFilterTag.CHOSEN.value,
    sub_tag_ids: str = "",
    chat_model_id: str = "",
    play_type: Optional[RolePlayType] = None,
    chat_type: Optional[RoleFilterChatType] = None,
    gender_type: Optional[RoleGenderType] = None,
    sort_type: str = "",
    limit: int = 10,
    offset: int = 0,
    current_language: str = Header(default=Language.ZH.value),
    eng_bot: str = Header(None),
):
    if limit not in [10, 20]:
        log.error("limit not in [10, 20],limit: %s", limit)
        raise exception_util.param_error("Param Error")
    filter = RoleFilterRequest(
        nsfw=nsfw,
        product_id=chat_model_id,
        real_role=chat_type.real_role() if chat_type else None,
        chat_types=chat_type.chat_types() if chat_type else [],
        play_type=play_type.value if play_type else "",
        language=current_language,
        offset=offset,
        limit=limit,
    )

    sub_tag_ids_splits = sub_tag_ids.split(",")
    ids = [int(x) for x in sub_tag_ids_splits if x.isdigit()]
    sub_tag_names = [x for x in sub_tag_ids_splits if not x.isdigit()]
    if sub_tag_ids_splits and ids:
        tag_list = await tag_service.list_sub_tag_by_ids(ids)
        filter.sub_tags = [tag.tag_name for tag in tag_list]
    if sub_tag_names:
        tag_list = await tag_service.list_sub_tag_by_names(
            sub_tag_names, language=current_language
        )
        filter.sub_tags = [tag.tag_name for tag in tag_list]
    if gender_type:
        filter.sub_tags.append(gender_type.to_tag_name())

    role_tag = RoleFilterTag(tag)
    ret = RoleFilterResponse()
    if role_tag == RoleFilterTag.CHOSEN:
        ret = await role_config_service.user_chosen_roles(filter)
    if role_tag == RoleFilterTag.HOT:
        ret = await role_config_service.user_hot_roles(filter)
    if role_tag == RoleFilterTag.NEW:
        ret = await role_config_service.user_new_roles(filter)
    if role_tag == RoleFilterTag.RANKING:
        sort_type = RoleSortType.LIKE.value if not sort_type else sort_type
        ste = RoleSortType(sort_type)
        ret = await role_config_service.user_ranking_roles(filter, ste)
    if role_tag == RoleFilterTag.GROUP:
        ret = await role_config_service.user_groups_roles(filter)
    if role_tag in RoleFilterTag.summary_ranking_tags():
        sort_type = role_tag.value if not sort_type else sort_type
        ste = RoleSortType(sort_type)
        ret = await role_config_service.user_ranking_roles(filter, ste)
    ret.latest_card_created_at = await role_loader_service.latest_card_created_at()
    # if eng_bot:
        # role_util.anonymous_response_authors(ret)
    return ret


@role_router.get("/roles/group/filter_list")
@limit_requests_by_tg("roles:group:filter_list", 5)
async def filter_group_list(request: Request, user_id: int = Depends(get_current_user)):
    groups = await role_group_service.list_public()
    return response_util.success({"groups": groups})


@role_router.get("/roles/group/detail")
@limit_requests_by_tg("roles:group:detail", 5)
async def roles_group_detail(
    request: Request,
    group_id: int,
    user_id: int = Depends(get_current_user),
    current_language: str = Header(default=Language.ZH.value),
):
    user = await user_service.get_user_by_id(user_id)
    group = await user_role_service.get_group_detail(user, group_id, current_language)
    return response_util.success({"group": group})


@role_router.post("/roles/create")
@limit_requests_by_tg("roles:create_role", 5)
async def create_role(
    request: Request,
    role_json: str = Form(...),
    user_id: int = Depends(get_current_user),
):
    log.info(f"create_role,user_id:{user_id},role_json: {role_json}")
    form_data = await request.form()
    avatar_img = form_data.get("avatar_img", None)
    await role_verify_service.input_create_error(role_json, user_id)
    role_dict: Dict[str, Any] = json.loads(role_json)
    role_dict = json_util.remove_null_dict(role_dict)
    input_role = RoleEditDetail(**role_dict)
    # input_role = role_util.remove_html_tag(input_role)
    return await role_config_service.create_role(input_role, avatar_img, user_id)


@role_router.post("/roles/update")
@limit_requests_by_tg("roles:update_role", 5)
async def update_role(
    request: Request,
    role_json: str = Form(...),
    publish_card: bool = Form(default=False),
    # avatar_img: Annotated[UploadFile, File()]=File(None),
    # avatar_img: Optional[UploadFile] = File(None),
    user_id: int = Depends(get_current_user),
    current_language: str = Header(default=Language.ZH.value),
):

    form_data = await request.form()
    avatar_img = form_data.get("avatar_img", None)
    log.info(f"update_role,user_id:{user_id},role_json: {role_json}")
    await role_verify_service.input_edit_error(str(role_json), user_id)
    #
    role_dict: Dict[str, Any] = json.loads(str(role_json))
    role_dict = json_util.remove_null_dict(role_dict)
    input_role = RoleEditDetail(**role_dict)

    user = await user_service.get_user_by_id(user_id)
    publish_privilege = json_util.convert_to_dict(user.publish_role_privilege)
    if publish_privilege and publish_privilege["reject_count"] >= 6 and publish_card:
        return response_util.error(ErrorCode.NOT_ALLOWED.value, "提审违规")

    # upload img
    avatar_img = cos_util.upload_image(avatar_img, CosPrefix.ROLE)
    input_role.role_avatar = avatar_img if avatar_img else input_role.role_avatar

    if publish_card and input_role.publish_verify() == False:
        return response_util.json_param_error("miss required param")

    products = await product_service.list_chat_product_new()
    product_ids = [x.mid for x in products]
    role_config = input_role.to_role_config(product_ids, uid=user_id)
    if input_role.role_book:
        book = await character_book_service.update_character_book(input_role.role_book)
        role_config.book_id = book.book_id
    if publish_card and role_config.sub_tags:
        sub_tags = await tag_service.list_sub_tag_by_names(
            json_util.convert_to_list(role_config.sub_tags), current_language
        )
        role_config.sub_tags = [tag.tag_name for tag in sub_tags]
    res = await RoleConfigService.update_user_role_config(
        uid=user_id, config=role_config
    )

    if not res:
        return response_util.json_param_error("Role not found")
    # update audit status
    # 发布卡，创建或者更新审核
    if publish_card:
        await role_audit_service.user_submit_audit(
            user_id, ChatModeType.SINGLE.value, res.id
        )
    # 未发布卡，更新为带审核状态
    if not publish_card:
        await role_audit_service.reset_status_edit(
            user_id, ChatModeType.SINGLE.value, res.id
        )
    return await role_config_service.load_role_edit_detail(res.id)


@role_router.post("/roles/disable")
async def disable_role(
    role_id: Annotated[int, Form()], user_id: int = Depends(get_current_user)
):
    role = await RoleConfigService.get_role_config(role_id)
    if not role or role.uid != user_id:
        return response_util.json_param_error("Role not found or not owner")
    audit = await role_audit_service.get_by_mode(ChatModeType.SINGLE.value, role_id)
    if audit and audit.status == AuditStatus.AUDITING.value:
        return response_util.error(ERROR_CODE.NOT_ALLOWED.value, "Role is auditing")
    role.status = False
    await role.save()
    products = await product_service.list_chat_product_new()
    product_ids = [x.mid for x in products]
    return RoleEditDetail.from_role_config(role, product_ids)


@role_router.post("/roles/group/disable")
async def disable_group(
    group_id: Annotated[int, Form()],
    user_id: int = Depends(get_current_user),
):
    log.info(f"disable_group,group_id:{group_id},user_id:{user_id}")
    audit = await role_audit_service.get_by_mode(ChatModeType.GROUP.value, group_id)
    if audit and audit.status == AuditStatus.AUDITING.value:
        return response_util.error(ERROR_CODE.NOT_ALLOWED.value, "Group is auditing")
    return await role_group_service.disable(user_id, group_id)


@role_router.get("/roles/group/create_config")
@limit_requests_by_tg("roles:create_group_config", 5)
async def create_group_config(
    request: Request,
    user_id: int = Depends(get_current_user),
    group_id: int = 0,
):
    return await role_group_service.create_config(group_id)


@role_router.post("/roles/group/create")
@limit_requests_by_tg("user:create_group", 10)
async def create_group(
    request: Request,
    chat_group: ChatGroupEdit,
    user_id: int = Depends(get_current_user),
):
    log.info(
        f"create_group,user_id:{user_id},chat_group_json: {chat_group.model_dump()}"
    )
    error = await role_verify_service.group_create_error(chat_group, user_id)
    if error:
        return response_util.json_param_error(error)
    role_list = await role_loader_service.list_by_ids(chat_group.role_ids)
    nsfw = any([role.nsfw for role in role_list])
    return await role_group_service.create_group(user_id, chat_group, nsfw)


@role_router.post("/roles/group/update")
@limit_requests_by_tg("user:update_group", 10)
async def update_group(
    request: Request,
    chat_group: UserChatGroupEdit,
    user_id: int = Depends(get_current_user),
):
    log.info(
        f"update_group,user_id:{user_id},chat_group_json: {chat_group.model_dump()}"
    )
    group_audit = await role_audit_service.get_role_audit(
        user_id, ChatModeType.GROUP.value, chat_group.id
    )
    if group_audit and group_audit.status == AuditStatus.AUDITING.value:
        return response_util.error(ERROR_CODE.PARAM_ERROR.value, "审核中的卡不能修改")

    group_edit = await role_group_service.load_chat_group_edit(chat_group.id)
    if not group_edit or group_edit.user_id != user_id:
        return response_util.json_param_error("Group not found or not owner")

    error = await role_verify_service.user_group_update_error(chat_group, user_id)
    if error:
        # return response_util.json_param_error(error)
        return response_util.response_param_error(error)

    user = await user_service.get_user_by_id(user_id)
    publish_privilege = json_util.convert_to_dict(user.publish_role_privilege)
    if (
        publish_privilege
        and publish_privilege["reject_count"] >= 6
        and chat_group.publish_card
    ):
        return response_util.error(ErrorCode.NOT_ALLOWED.value, "提审违规")

    # 提交发布卡的时候，角色ID列表需相同
    if chat_group.publish_card and group_audit and group_audit.open_group_id:
        publish_role = await role_group_service.load_chat_group_edit(
            group_audit.open_group_id
        )
        if publish_role and not object_util.equals_list(
            publish_role.role_ids, chat_group.role_ids
        ):
            return response_util.error(
                ERROR_CODE.PARAM_ERROR.value, "发布失败，与线上卡角色列表不一致"
            )
    return await role_group_service.user_update_group(user_id, chat_group)


# sub tags
@role_router.get("/queryTags")
async def query_tags() -> list[str]:
    all_tags = await tag_service.list_sub_tags_with_enabled()
    return [tag.tag_name for tag in all_tags]


@role_router.get("/tag_orders")
async def get_tags_order():
    tag_orders = await tag_service.list_tags_with_orders()
    return JSONResponse(content=tag_orders)


@role_router.post("/tokenizers/count")
async def get_token_count(input: dict = {}):
    ret = token_util.num_token_from_dict(input)
    if "content" in input:
        token_count = token_util.num_tokens_from_string(input["content"])
        ret["token_count"] = token_count
    return JSONResponse(content=ret)


@role_router.post("/tools/upload/img")
async def upload_img(
    img: UploadFile = File(None),
    category: UploadImageCategory = Form(...),
    user_id: int = Depends(get_current_user),
):
    if category == UploadImageCategory.CHAT_BACKGROUND:
        img_url = cos_util.upload_image(img, CosPrefix.CHAT_BACKGROUND)
        log.info(f"upload_img,user_id:{user_id},category:{category},img_url:{img_url}")
        return response_util.ok({"img_url": img_url})
    return response_util.def_error("category error")

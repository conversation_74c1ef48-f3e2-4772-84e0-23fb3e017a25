import logging, os
import urllib.parse
from aiogram import Bo<PERSON>
from aiogram.types import CallbackQuery, Message, PreCheckoutQuery
from aiogram.fsm.context import FSMContext
from aiogram.utils.keyboard import InlineKeyboardBuilder
from aiogram.filters.callback_data import Callback<PERSON><PERSON>
from common import bot_common
from common.common_constant import BotCategory, RechargeRouteStrategy
from controllers.recharge import RechargeProductResponse
from persistence.models.models import RechargeStatusEnum, User, RechargeChannelConfig
from common.bot_common import RechargeNavCallback, charge_url
from services import recharge_service, tg_config_service, tmpay_recharge_service, star_payment_service, recharge_channel_service
from services.user_service import user_service
from services.out_recharge_common import RechargeRequest
from .bot_setting import router


async def handle_recharge_with_dynamic_routing(user_id: int, recharge_product_id: str, pay_type: str, bot_id: int):
    from controllers.recharge_handler import handle_recharge_request_base

    req = RechargeRequest(recharge_id=recharge_product_id, 
                          type=pay_type, bot_id=bot_id)
    client_ip = '127.0.0.1'

    channel_config = await recharge_channel_service.get_by_product_id(req.recharge_id)
    if channel_config is None:
        channel_config = RechargeChannelConfig.default_config()

    result = await handle_recharge_request_base(
        user_id=user_id,
        req=req,
        client_ip=client_ip,
        channel_config=channel_config,
        current_language='zh'
    )

    return result

class RechargeProductCallback(CallbackData, prefix='rp'):
    pid: str

class PayMethodCallback(CallbackData, prefix='pm'):
    type: str


async def recharge_tip_reply(bot: Bot, user_id: int):
    builder = await bot_common.create_charge_button_builder(bot, user_id)
    recharge_products = await recharge_service.list_user_products(user_id)
    recharge_products.sort(key=lambda x: x.order)
    rp_desc = [ f'{rp.content_for_bot} - {rp.promotion_desc}' for rp in recharge_products ]
    return f'''【充值套餐更新，买越多送越多，手慢无】

{'\n'.join(rp_desc)}

推荐微信、支付宝、信用卡，钻石直接到账。
''', builder.as_markup()

async def callback_recharge(query: CallbackQuery, bot: Bot, state: FSMContext) -> None:
    # gfk = redis_client.get('bot_charge_sdfkw')
    return await _recharge_helper(query.message, query.from_user.id, bot, state)

async def command_recharge(message: Message, bot: Bot, state: FSMContext) -> None:
    # gfk = redis_client.get('bot_charge_sdfkw')
    return await _recharge_helper(message, message.from_user.id, bot , state)

async def _recharge_helper(message: Message, tg_user_id: int, bot: Bot, state: FSMContext) -> None:
    # gfk = redis_client.get('bot_charge_sdfkw')
    user = await user_service.get_user_by_tg_id(tg_user_id)
    
    if user is None:
        user = await user_service.register_by_tg(tg_user_id=tg_user_id, first_name=message.from_user.first_name, last_name=message.from_user.last_name,user_name=message.from_user.username)
    tip, markup = await recharge_tip_reply(bot, user.id)
    await message.answer(tip, parse_mode="html",reply_markup=markup)
    await state.set_state(None)
    await state.update_data({'pay_type': None})

async def send_recharge(bot:Bot,user:User,tg_id:int, state: FSMContext):
    tip, markup = await recharge_tip_reply(bot, user.id)
    await bot.send_message(tg_id, tip, reply_markup=markup)
    await state.update_data({'pay_type': None})

async def send_voucher_message(query: CallbackQuery, bot: Bot, state: FSMContext):
    builder = InlineKeyboardBuilder()
    builder.button(text="立即充值", url=charge_url)
    builder.button(text="返回", callback_data=RechargeNavCallback(path='recharge'))
    cbot = await tg_config_service.get_main_bot_by_category(BotCategory.CUSTOMER)
    await query.message.edit_text(f'''点击下方按钮，进入发卡平台，选择对应套餐，充值即可。
充值成功后，你将收到一串兑换卡密，请复制卡密，使用 `/voucher` 命令，在消息中直接输入卡密，发送给我，完成卡密兑换💎。

⚠️注意：如果发卡平台打开缓慢，可尝试关闭VPN重新加载，付款完成后再重新打开VPN。

如有任何问题，请联系官方客服。 @{cbot.username}

如果没获取／没记住卡密，可在网站通过订单编号／联系方式查询： https://www.sdfkw.xyz/orderquery
卡密问题最快解决路径：找迅雷发卡网客服 @xlfkwkf_bot
''', reply_markup=builder.as_markup())

async def send_recharge_product_message(query: CallbackQuery, callback_data: RechargeNavCallback, state: FSMContext,user_id:int):
    recharge_products = await recharge_service.list_user_products(user_id)
    id_content_map = {str(p.recharge_product_id): f'{p.content_for_bot} - {p.promotion_desc}' for p in recharge_products}
    products:list[RechargeProductResponse] = []
    for p in recharge_products:
        # if callback_data.type == 'wechat' and p.cny_price <= 1000000:
        #     continue
        products.append(RechargeProductResponse.from_model(p))
    products.sort(key=lambda x: x.order)
    builder = InlineKeyboardBuilder()
    product_desc = []
    for p in products:
        builder.button(text=f"{p.title}({p.original_price_desc})", callback_data=RechargeProductCallback(pid=p.recharge_product_id))
        product_desc.append(id_content_map[p.recharge_product_id])
    builder.button(text="返回", callback_data=RechargeNavCallback(path='recharge'))
    builder.adjust(1)
    desc_text = '\n'.join(product_desc)
    await state.update_data({'pay_type': callback_data.type})
    await query.message.edit_text(f'''请选择购买套餐\n\n{desc_text}''', reply_markup=builder.as_markup())

async def send_pay_method_message(query: CallbackQuery, bot: Bot, state: FSMContext):
    builder = InlineKeyboardBuilder()
    builder.button(text="支付宝", callback_data=PayMethodCallback(type='alipay'))
    builder.button(text="微信", callback_data=PayMethodCallback(type='wechat'))
    builder.button(text="返回", callback_data=RechargeNavCallback(path='product'))
    await query.message.edit_text('点击下方按钮，选择充值方式，完成充值。', reply_markup=builder.as_markup())

async def on_product_selected(query: CallbackQuery, recharge_product_id: str, bot: Bot, state: FSMContext):
    user: User = await user_service.get_user_by_tg_id(query.from_user.id) # type: ignore
    data = await state.get_data()
    type = data.get('pay_type')
    customer_service_bot = await tg_config_service.get_main_bot_by_category(BotCategory.CUSTOMER)
    customer_service_bot = customer_service_bot.username
    recharge_product = await recharge_service.get_recharge_product(recharge_product_id)
    builder = InlineKeyboardBuilder()
    builder.button(text="返回", callback_data=RechargeNavCallback(path='product', type=type))
    if recharge_product is None:
        await query.message.edit_text(f'充值失败，请稍后重试, 或者联系客服 @{customer_service_bot}', reply_markup=builder.as_markup())
        return

    if type == 'star':
        star_payment, prices = await star_payment_service.create_star_payment_order_with_product(user.id, query.from_user.id, recharge_product, bot.id)
        if star_payment is None:
            await query.message.edit_text(f'充值失败，请稍后重试, 或者联系客服 @{customer_service_bot}', reply_markup=builder.as_markup())
            return
        await bot.send_invoice(query.from_user.id, '幻梦AI充值套餐', recharge_product.title, str(star_payment.invoice_id), 'XTR', prices)
        return
    elif type == 'stripe':
        return_host = os.environ['WEB_APP_URL'].rstrip('/')
        url = await recharge_service.create_checkout_session(user.id, recharge_product, return_host, bot.id)
        if len(url) == 0:
            await query.message.edit_text(f'充值失败，请稍后重试, 或者联系客服 @{customer_service_bot}', reply_markup=builder.as_markup())
            return
        pay_url = url
    else:
        builder.button(text='备用渠道', callback_data=RechargeProductCallback(pid=recharge_product_id))
        result = await handle_recharge_with_dynamic_routing(user.id, recharge_product_id, type, bot.id)
        
        if not result.success:
            await query.message.edit_text(f'充值失败：{result.message}，请稍后重试, 或者联系客服 @{customer_service_bot}', reply_markup=builder.as_markup())
            return

        pay_url = result.pay_url

    rep = RechargeProductResponse.from_model(recharge_product)
    await query.message.edit_text(text=f'您选择了{rep.title}({rep.promotion_desc}) - {rep.original_price_desc}\n请点击下方链接完成充值:\n\n {pay_url}\n\n（提示：卡密购买中也有「微信支付」哦）',
                           reply_markup=builder.as_markup())

@router.callback_query(RechargeNavCallback.filter())
async def handle_voucher_cancel(query: CallbackQuery, callback_data: RechargeNavCallback, bot: Bot, state: FSMContext):
    user = await user_service.get_user_by_tg_id(query.from_user.id)
    if callback_data.path == 'recharge':
        tip, markup = await recharge_tip_reply(bot, user.id)
        await query.message.edit_text(tip, reply_markup=markup)
        await state.update_data({'pay_type': None})
    elif callback_data.path == 'voucher':
        await send_voucher_message(query, bot, state)
    elif callback_data.path == 'product':
        await send_recharge_product_message(query, callback_data, state,user.id)
    elif callback_data.path == 'method':
        await send_pay_method_message(query, bot, state)

@router.callback_query(RechargeProductCallback.filter())
async def handle_recharge_product(query: CallbackQuery, bot: Bot, callback_data: RechargeProductCallback, state: FSMContext):
    await on_product_selected(query, callback_data.pid, bot, state)

@router.callback_query(PayMethodCallback.filter())
async def handle_pay_method(query: CallbackQuery, bot: Bot, callback_data: PayMethodCallback, state: FSMContext):
    # await on_product_selected(query, bot, callback_data, state)
    ...

@router.pre_checkout_query()
async def pre_checkout_query_handler(query: PreCheckoutQuery, 
                                     bot: Bot, state: FSMContext):
    payment_id = query.invoice_payload
    order = await star_payment_service.get_star_payment_order(payment_id, query.from_user.id)
    if order is None:
        await query.answer(False, '订单不存在')
        return
    if order.status == RechargeStatusEnum.SUCCEED:
        await query.answer(False, '订单已支付')
        return
    if order.status != RechargeStatusEnum.INIT:
        await query.answer(False, '订单状态错误')
        return
    await query.answer(True)

async def handle_successful_payment(message: Message, bot: Bot, state: FSMContext):
    if not message.successful_payment:
        return
    await star_payment_service.on_star_payment_success(message.successful_payment)
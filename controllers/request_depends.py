from functools import wraps
import logging
import time
from fastapi import Request
from fastapi.responses import JSONResponse
from jose import JWTError, jwt
from common.common_constant import ApiSource, Env
from persistence import redis_client
from persistence.models.models import UserRegisterSource
from utils import env_util, tg_util
import functools
import logging

US_WEB_ORIGIN = [
    "usa-tavern.655356.xyz",
    "usa-tavern-api.655356.xyz",
    "fancyou.ai",
    "www.fancyou.ai",
]
API_SOURCE_CONFIG = {
    "usa-tavern.655356.xyz": ApiSource.US_WEB,
    "usa-tavern-api.655356.xyz": ApiSource.US_WEB,
    "fancyou.ai": ApiSource.US_WEB,
    "www.fancyou.ai": ApiSource.US_WEB,
    "tavern-web-api.655356.xyz": ApiSource.HM_WEB,
    "tavern-api.huanmeng.ai": ApiSource.HM_WEB,
    "huanmeng.ai": ApiSource.HM_WEB,
    "www.huanmeng.ai": ApiSource.HM_WEB,
    "uhoney-tavern-api.655356.xyz": ApiSource.OVERSEAS_WEB,
    "uhoney-tavern.655356.xyz": ApiSource.OVERSEAS_WEB,
    "uhoney.ai": ApiSource.OVERSEAS_WEB,
    "www.uhoney.ai": ApiSource.OVERSEAS_WEB,
    "tavern-api.655356.xyz": ApiSource.TMA,
    "tavern.655356.xyz": ApiSource.TMA,
}

WEB_ORIGIN = [
    # "tavern-web-api.655356.xyz",
    "uhoney-tavern-api.655356.xyz",
]
log = logging.getLogger(__name__)


async def dep_api_source(request: Request) -> ApiSource:
    # 获取当前请求的网址
    req_host = request.url.hostname
    origin = request.headers.get("origin")
    if origin and origin.startswith("https://"):
        origin = origin[8:]
    api_source = None
    if req_host and req_host in API_SOURCE_CONFIG:
        api_source = API_SOURCE_CONFIG[req_host]
    if not api_source and origin and origin in API_SOURCE_CONFIG:
        api_source = API_SOURCE_CONFIG[origin]
    api_source = api_source if api_source else ApiSource.TMA
    # log.info(
    #     f"api_source: req_host={req_host}, origin={origin}, api_source={api_source}"
    # )
    return api_source


async def dep_register_source(request: Request) -> str:
    # 获取当前请求的网址
    req_url = request.url.hostname
    origin = request.headers.get("origin")
    log.info(f"dep_register_source: req_url={req_url}, origin={origin}")
    if req_url and [mid for mid in US_WEB_ORIGIN if mid in req_url]:
        return UserRegisterSource.USA_WEB.value
    if origin and [mid for mid in US_WEB_ORIGIN if mid in origin]:
        return UserRegisterSource.USA_WEB.value
    if req_url and [mid for mid in WEB_ORIGIN if mid in req_url]:
        return UserRegisterSource.WEB.value
    if origin and [mid for mid in WEB_ORIGIN if mid in origin]:
        return UserRegisterSource.WEB.value

    return UserRegisterSource.TMA.value


async def dep_language(request: Request) -> str:
    return request.headers.get("Current-Language", "")


SECRET_KEY = "b'g5gFjPqzsi0V4sNUO9q5GyvYb1uiKvZ0'"
ALGORITHM = "HS256"


def get_user_id_by_token(token: str) -> int:
    if not token:
        return 0
    try:
        payload = jwt.decode(token, SECRET_KEY, algorithms=ALGORITHM)
        user_id_str: str = payload.get("sub", "0")
        return int(user_id_str)
    except JWTError as e:
        print(e)
        log.warning("Could not decode token,token: %s", token)
        return 0


def limit_request_by_key(key: str, expire=10):
    def decorator(func):
        @wraps(func)
        async def wrapper(*args, **kwargs):
            if not redis_client.acquire_lock(key, key, expire):
                log.error("Too Many Requests, key:%s", key)
                return JSONResponse(
                    content="重复请求，请稍后再试",
                    status_code=429,
                )
            try:
                return await func(*args, **kwargs)
            finally:
                redis_client.release_lock(key, key)

        return wrapper

    return decorator


# 使用redis进行去重
def limit_requests_by_tg(prefix: str, expire=10):
    def decorator(func):
        @wraps(func)
        async def wrapper(request: Request, *args, **kwargs):
            if env_util.get_current_env() == Env.STAG:
                return await func(request, *args, **kwargs)
            # header中获取用户id
            token = request.cookies.get("token", "")
            user_id = get_user_id_by_token(token)
            tg_init_data = request.headers.get("tg-init-data", "")
            if user_id == 0:
                user_id = tg_util.get_user_id_by_tg(tg_init_data)
            if user_id == 0:
                log.error("user_id is 0,token:%s,tg_init_data:%s", token, tg_init_data)
                return await func(request, *args, **kwargs)
            if not redis_client.acquire_lock(prefix, str(user_id), expire):
                params = request.query_params
                bd_str = ""
                body = await request.body()
                if not body:
                    bd_str = body.decode("utf-8")
                log.info("request tg_id:%s,params:%s,body: %s", user_id, params, bd_str)
                log.warning("Too Many Requests, user_id: %s,key:%s", user_id, prefix)
                return JSONResponse(
                    content="Too Many Requests",
                    status_code=429,
                )
            # return await func(request, *args, **kwargs)

            try:
                return await func(request, *args, **kwargs)
            finally:
                redis_client.release_lock(prefix, str(user_id))

        return wrapper

    return decorator


def async_timeit_decorator(func):
    @functools.wraps(func)
    async def async_wrapper(*args, **kwargs):
        start_time = time.time()
        result = await func(*args, **kwargs)  # 异步等待函数执行
        end_time = time.time()
        log.info(f"{func.__name__} cost: {end_time - start_time:.4f} s")
        return result

    return async_wrapper


def timeit_decorator(func):
    @functools.wraps(func)
    def sync_wrapper(*args, **kwargs):
        start_time = time.time()
        result = func(*args, **kwargs)  # 同步执行函数
        end_time = time.time()
        log.info(f"{func.__name__} cost: {end_time - start_time:.4f} s")
        return result

    return sync_wrapper

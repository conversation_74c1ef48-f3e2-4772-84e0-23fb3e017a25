import logging
from decimal import *
from fastapi import APIRouter, Depends, Header
from pydantic import BaseModel, Field
from common.entity import MsgResponse
from controllers.user_check import get_current_user
from persistence.models.models import (
    RechargeChannelConfig,
    RechargeProduct,
)
from services import recharge_channel_service, recharge_service
from services.user_service import user_service
from utils.translate_util import _t, _tl


recharge_router_v2 = APIRouter()

log = logging.getLogger(__name__)

class RechargeProductResponseV2(BaseModel):
    recharge_product_id: str
    title: str
    desc: str
    price: int
    display_price: str
    corner_title: str
    corner_tip: str
    amount: int
    reward_amount: int
    total_amount: int
    recharge_desc: str
    promotion_desc: str
    order: int = 0
    supported_pay_types: list[str] = []
    disabled: bool = False
    disabled_message: str = ''

    @staticmethod
    def from_model(product: RechargeProduct, lang: str) -> "RechargeProductResponseV2":
        display_price = str(Decimal(product.price) / 100 / 1000)
        cny_display_price = str(Decimal(product.cny_price) / 100 / 1000)
        display_price = f"${display_price}{_t('美元', lang)}（{cny_display_price}{_t('元',lang)}）"
        price_desc = display_price
        if product.original_price_desc:
            price_desc = product.original_price_desc
            display_price = product.original_price_desc
        return RechargeProductResponseV2(
            recharge_product_id=str(product.recharge_product_id),
            title=product.title,
            desc=product.desc,
            recharge_desc=price_desc,
            price=product.price,
            display_price=display_price,
            corner_title=product.corner_title,
            corner_tip=product.corner_tip,
            amount=product.amount,
            reward_amount=product.reward_amount,
            total_amount=product.amount + product.reward_amount,
            promotion_desc=product.promotion_desc,
            order=product.order,
            disabled=product.disabled(),
            disabled_message=_tl(product.disabled_message, lang) if product.disabled_message else '',
        )

class RechargeListResponse(MsgResponse):
    products: list[RechargeProductResponseV2] = Field(..., description="充值档位列表")
    remarks: list[str]
    banner_tip: str = ''

# 返回充值档位
@recharge_router_v2.get("/recharge/list/v2")
async def recharge_list(
    current_language: str = Header(None), user_id: int = Depends(get_current_user),
    eng_bot: str = Header(None)
) -> RechargeListResponse:
    is_pay_user = await user_service.is_payed_user(user_id)
    recharge_products = await recharge_service.list_user_products(user_id)
    products: list[RechargeProductResponseV2] = []
    for p in recharge_products:
        products.append(RechargeProductResponseV2.from_model(p, current_language))
    configs = await recharge_channel_service.get_configs()
    config_dict = {str(config.recharge_product_id): config for config in configs}
    products.sort(key=lambda x: x.order)
    for product in products:
        product.title = _tl(product.title, current_language,RechargeProduct.__name__)
        product.desc = _tl(product.desc, current_language,RechargeProduct.__name__)
        product.promotion_desc = _tl(product.promotion_desc, current_language,RechargeProduct.__name__)
        channel_config = config_dict.get(product.recharge_product_id)
        if channel_config is not None:
            product.supported_pay_types = [type for type in channel_config.supported_pay_types() if type != "CLOSED"]
        else:
            product.supported_pay_types = RechargeChannelConfig.all_pay_types()
        product.corner_title = _tl(product.corner_title, current_language,RechargeProduct.__name__)
        product.corner_tip = _tl(product.corner_tip, current_language,RechargeProduct.__name__)
        product.recharge_desc = _tl(product.recharge_desc, current_language,RechargeProduct.__name__)
        product.display_price = _tl(product.display_price, current_language,RechargeProduct.__name__)
        if eng_bot:
            product.corner_title = _tl(product.corner_title, current_language)
            display_price = str(Decimal(product.price) / 100 / 1000)
            product.display_price = f'${display_price}'
            if 'alipay' in product.supported_pay_types:
                product.supported_pay_types.remove('alipay')
            if 'wechat' in product.supported_pay_types:
                product.supported_pay_types.remove('wechat')
            if 'voucher' in product.supported_pay_types:
                product.supported_pay_types.remove('voucher')

    remarks = recharge_products[0].remarks.splitlines()
    if not is_pay_user:
        banner_tip = _tl('充多赚多，仅首充', current_language,RechargeProduct.__name__)
    else: 
        banner_tip = ''
    remarks = [_tl(remark, current_language,RechargeProduct.__name__) for remark in remarks if remark]
    if eng_bot:
        remarks = ['Original-price 💎 permanently valid + bonus 🟡 also permanently valid.']
    return RechargeListResponse(products=products, remarks=remarks, banner_tip=banner_tip)

from fastapi import APIRouter, Depends, Header
from common.common_constant import (
    ApiSource,
    Language,
    ProductType,
    RoleFilterChatType,
    RoleFilterTag,
    RoleGenderType,
    RolePlayType,
    RoleSortType,
    RoleTag,
    UserBenefitCategory,
)
from common.role_model import SelectOption
from controllers.request_depends import dep_api_source
from persistence.models.models import Product, RoleConfig, SubTag
from services.role import role_loader_service
from utils import env_const
from services import product_service, tag_service, tg_config_service, welfare_service
from utils.translate_util import _t, _tl

config_router = APIRouter()


@config_router.get("/config")
async def get_config(
    current_language: str = Header(default=Language.ZH.value),
    api_source: ApiSource = Depends(dep_api_source),
):
    bots = await tg_config_service.swf_bots()
    tma_ids = [tg_config.tma_bot_id for tg_config in bots if tg_config.tma_bot_id]
    ret = {"sfw_tma": tma_ids}
    ret["latest_card_created_at"] = await role_loader_service.latest_card_created_at()  # type: ignore
    configs = [
        await chat_products_config(current_language, api_source),
        await chat_models_config(current_language),
        await role_tags_config(current_language),
        await summary_ranking_tags(current_language),
        await role_gender_config(current_language),
        await play_types_config(current_language),
        await chat_types_config(current_language),
        await sub_tags_config(current_language),
        await sub_tag_category_config(current_language),
        await sort_types_config(current_language),
    ]
    for key, val in configs:
        ret[key] = val
    publish_task_switch = await welfare_service.publish_task_switch()
    ret["publish_task_switch"] = publish_task_switch  # type: ignore
    
    # user_benefit_category
    user_benefit_category_list = [x for x in UserBenefitCategory]
    ubc_desc_map = UserBenefitCategory.to_desc_map()
    user_benefit_category_list = [
        SelectOption.init(tag.value, ubc_desc_map.get(tag.value, tag.value))
        for tag in user_benefit_category_list
    ]
    for ubc in user_benefit_category_list:
        ubc.name = _tl(ubc.name, current_language, UserBenefitCategory.__name__)
    ret["user_benefit_category_list"] = user_benefit_category_list  # type: ignore
    return ret

async def chat_products_config(language: str, api_source: ApiSource):
    chat_products = await product_service.list_display_chat_products_v1(
        language, api_source
    )
    return "chat_products", chat_products

async def sort_types_config(language: str):
    sort_types = [SelectOption.init(tag.value, tag.value) for tag in RoleSortType]
    for sort_type in sort_types:
        sort_type.name = _tl(sort_type.name, language, RoleConfig.__name__)
    return "sort_types", sort_types


async def sub_tag_category_config(language: str):
    sub_tag_categories = await tag_service.list_sub_tag_category()
    sub_tag_categories = [SelectOption.init(tag, tag) for tag in sub_tag_categories]
    for sub_tag in sub_tag_categories:
        sub_tag.name = _tl(sub_tag.name, language, SubTag.__name__)
    return "sub_tag_categories", sub_tag_categories


async def sub_tags_config(language: str):
    ret_sub_tags = await tag_service.list_sub_tags_with_enabled()
    categories = await tag_service.list_sub_tag_category()
    sub_tags = [
        SelectOption.init(str(tag.id), tag.tag_name, tag.category)
        for tag in ret_sub_tags
        if tag.category in categories
    ]
    for sub_tag in sub_tags:
        sub_tag.name = _tl(sub_tag.name, language, SubTag.__name__)
        sub_tag.category = _tl(sub_tag.category, language, SubTag.__name__)
    return "sub_tags", sub_tags


async def play_types_config(language: str):
    play_types = [SelectOption.init(tag.value, tag.value) for tag in RolePlayType]
    for play_type in play_types:
        play_type.name = _tl(play_type.name, language, RoleConfig.__name__)
    return "play_types", play_types

async def role_gender_config(language: str):
    role_genders = [SelectOption.init(tag.value, tag.value) for tag in RoleGenderType]
    for role_gender in role_genders:
        role_gender.name = _tl(role_gender.name, language, RoleConfig.__name__)
    return "role_genders", role_genders

async def chat_types_config(language: str):
    role_tags = [SelectOption.init(tag.value, tag.value) for tag in RoleFilterChatType]
    for role_tag in role_tags:
        role_tag.name = _tl(role_tag.name, language, RoleConfig.__name__)
    return "chat_types", role_tags


async def role_tags_config(language: str):
    role_tags = [
        SelectOption.init(tag.value, tag.value) for tag in RoleFilterTag.display_tags()
    ]
    for role_tag in role_tags:
        role_tag.name = _tl(role_tag.name, language, RoleTag.__name__)
    return "role_tags", role_tags


async def summary_ranking_tags(language: str):
    summary_ranking_tags = [
        SelectOption.init(tag.value, tag.value)
        for tag in RoleFilterTag.summary_ranking_tags()
    ]
    for summary_ranking_tag in summary_ranking_tags:
        summary_ranking_tag.name = _tl(
            summary_ranking_tag.name, language, RoleTag.__name__
        )
    return "summary_rank_tags", summary_ranking_tags


async def chat_models_config(language: str):
    products = await product_service.list_original_products(ProductType.CHAT, True)
    products.sort(key=lambda x: x.price)
    chat_models = [
        SelectOption.init(product.mid, product.short_name) for product in products
    ]
    for chat_model in chat_models:
        chat_model.name = _tl(chat_model.name, language, Product.__name__)
    return "chat_models", chat_models

import asyncio
import logging
import os
import random
import re
from fastapi import Form, <PERSON><PERSON>, Depen<PERSON>, APIRouter, Request
from pydantic import BaseModel
from sse_starlette.sse import EventSourceResponse
from fastapi.responses import JSONResponse
from aioitertools import itertools as aioitertools
from ai import lite_llm_bot, new_chat_bot
from common.models.chat_model import (
    ChatNextInput,
    GenerateSbtRequest,
    HistoryRequest,
    SaveUserSbtRequest,
    SbtDetail,
    UserChatBalanceResponse,
)
from common.models.chat_request import (
    ChatRequest,
    DelMessageRequest,
    ImpersonateRequest,
    UpdateModelRequest,
    UpdateModelRequest,
    UserInputResponse,
)
from controllers import user_check
from controllers.request_depends import limit_requests_by_tg
from controllers.user_check import get_current_user, user_service
from persistence import chat_history_dao, redis_client
from persistence.models.models import SbtConfig
from persistence.presets import <PERSON><PERSON><PERSON>
from common.common_constant import (
    ERROR_CODE,
    Cha<PERSON><PERSON><PERSON><PERSON>ype,
    Chat<PERSON>latform,
    <PERSON><PERSON>r<PERSON><PERSON>,
    <PERSON><PERSON><PERSON><PERSON><PERSON>,
    <PERSON>,
    ProductType,
    UserBenefitCategory,
)
from services import (
    product_service,
    model_service,
    product_service,
    role_access_service,
    user_role_service,
    welfare_service,
)
from services import message_service
from services import account_service
from services.account_service import AccountService
from services.chat import (
    chat_message_service,
    chat_model_switch_history,
    chat_model_switch_history,
    chat_result_service,
    chat_snapshot_service,
)
from services.role import role_loader_service
from services.user import user_benefit_service, user_chat_service
from utils import (
    exception_util,
    response_util,
    str_util,
)
from utils.translate_util import _tl

chat_api_router = APIRouter()

log = logging.getLogger(__name__)

home_dir = os.path.expanduser("~")
failed_dir = os.path.join(home_dir, "asr_failed")

UNAUTHORIZED = JSONResponse(status_code=401, content={"message": "Unauthorized"})


# 模版信息
@chat_api_router.get("/user/chat/status_block/info")
async def get_status_block_info(
    conversation_id: str,
    message_id: str = "",
    version: int = 0,
    user_id: int = Depends(get_current_user),
    current_language: str = Header(default=Language.ZH.value),
):
    chat_message = await chat_history_dao.get_by_message_id_and_version(
        message_id, version
    )
    if not chat_message or chat_message.user_id != user_id:
        raise exception_util.verify_exception(message="消息不存在")
    user = await user_service.get_user_by_id(user_id)
    role_config = await role_loader_service.load_translated_role(
        chat_message.role_id, current_language, user.nickname, True
    )
    if not role_config:
        raise exception_util.verify_exception(message="角色配置不存在")
    templates = await user_chat_service.list_stb_by_user(
        user, role_config, current_language
    )
    user_sbt_detail = await user_chat_service.get_chat_display_usb(
        user, role_config, chat_message
    )
    product = await product_service.get_display_by_type_first(
        ProductType.GENERATE_STATUS_BLOCK.value
    )
    return response_util.ok(
        {"templates": templates, "user_sbt_detail": user_sbt_detail, "product": product}
    )


# 生成状态栏
@chat_api_router.post("/user/chat/status_block/generate")
@limit_requests_by_tg(prefix="user:chat:generate_status_block", expire=30)
async def generate_status_block(
    request: Request,
    body: GenerateSbtRequest,
    user_id: int = Depends(get_current_user),
    current_language: str = Header(default=Language.ZH.value),
):
    log.info(f"GenerateStatusBlock user_id:{user_id},request: {body}")
    await user_chat_service.generate_status_block_verify(user_id, body)
    content = await user_chat_service.generate_status_block(
        user_id, current_language, body
    )
    return response_util.ok({"content": content})


# 保存状态栏
@chat_api_router.post("/user/chat/status_block/save")
@limit_requests_by_tg(prefix="user:chat:save_status_block", expire=10)
async def save_status_block(
    request: Request,
    body: SaveUserSbtRequest,
    user_id: int = Depends(get_current_user),
    current_language: str = Header(default=Language.ZH.value),
):
    log.info(f"SaveStatusBlock user_id:{user_id},request: {body}")
    product = await product_service.get_online_by_type_first(
        ProductType.SAVE_STATUS_BLOCK.value
    )
    if not product:
        raise exception_util.verify_exception(message="产品配置有误，请联系客服")
    user = await user_service.get_user_by_id(user_id)
    chat_message = await chat_history_dao.get_by_message_id_and_version(
        body.message_id, body.version
    )
    if not chat_message:
        raise exception_util.verify_exception(message="消息不存在")
    role_config = await role_loader_service.load_translated_role(
        chat_message.role_id, current_language, user.nickname, True
    )
    if not role_config:
        raise exception_util.verify_exception(message="角色配置不存在")
    error_words = str_util.is_prohibited_words(body.content)
    if error_words:
        message = _tl(
            "状态栏审核不通过\n存在敏感词，请修改之后再次保存，AI再次审核，异常内容如下：",
            current_language,
        )
        return response_util.error(
            error_code=ErrorCode.ILLEGAL_CONTENT.value,
            message=f"{message}{','.join(error_words)}",
        )
    if not body.ai_generate:
        total_balance = await account_service.AccountService.get_total_balance(user_id)
        if total_balance < product.price:
            raise exception_util.verify_exception(
                error_code=ErrorCode.INSUFFICIENT_BALANCE.value,
                error_key=ErrorKey.INSUFFICIENT_BALANCE.value,
            )
    if body.sbt_id and not await user_chat_service.check_config_exists(body.sbt_id):
        raise exception_util.verify_exception(message="状态栏配置不存在")

    body.content = str_util.replace_nickname_placeholder(body.content, user.nickname)
    body.rule = str_util.replace_nickname_placeholder(body.rule, user.nickname)
    sbt = await user_chat_service.save_user_status_block(
        user_id, body, product, chat_message
    )
    if not sbt:
        raise exception_util.verify_exception(message="状态栏保存失败")
    await user_chat_service.update_message_status_block(
        body.message_id, body.version, sbt.content
    )
    chat_message = await chat_message_service.get_display_message(
        user_id=user_id,
        message_id=body.message_id,
        version=body.version,
        language=current_language,
        user_sbt=sbt,
    )
    return response_util.ok({"user_stb_detail": sbt, "chat_message": chat_message})


@chat_api_router.get("/user/chat/balance")
async def get_user_balance(
    user_id: int = Depends(get_current_user),
):
    user = await user_service.get_user_by_id(user_id)
    if not user:
        return UNAUTHORIZED
    payed_balance = await AccountService.get_payed_total_balance(user_id)
    reward_balance = await AccountService.get_reward_total_balance(user_id)
    benefits = await user_benefit_service.map_valid_by_all_product_mids(user)
    payed_user = await user_service.is_payed_user(user_id)
    response = UserChatBalanceResponse(
        payed_balance=payed_balance,
        reward_balance=reward_balance,
        benefits=benefits,
        payed_user=payed_user,
    )
    return response_util.ok(response.model_dump())


@chat_api_router.get("/user/chat/benefit")
@limit_requests_by_tg(prefix="user:chat:benefit", expire=5)
async def get_user_benefit(
    request: Request,
    user_id: int = Depends(get_current_user),
    user_benefit_category: str = "",
    current_language: str = Header(default=Language.ZH.value),
):
    user = await user_service.get_user_by_id(user_id)
    count = await user_benefit_service.chat_model_count(user)
    benefit_list = await user_benefit_service.list_valid(
        user, user_benefit_category, current_language
    )

    ## 更新奖励已读
    asyncio.create_task(welfare_service.read_received_reward(user_id))
    return response_util.success(
        {"chat_free_count": count, "benefit_list": benefit_list}
    )


@chat_api_router.get("/user/chat/roles")
@limit_requests_by_tg(prefix="user:chat:roles", expire=5)
async def get_user_roles(
    request: Request,
    user_id: int = Depends(get_current_user),
    current_language: str = Header(default=Language.ZH.value),
):
    user = await user_service.get_user_by_id(user_id)
    ret = await user_role_service.create_roles(user, current_language)
    return response_util.success(ret)


@chat_api_router.get("/user/chat/recent")
async def user_chat_recent(
    request: Request,
    offset: int = 0,
    limit: int = 15,
    user_id: int = Depends(get_current_user),
    current_language: str = Header(default=Language.ZH.value),
):
    user = await user_service.get_user_by_id(user_id)
    count, ret = await user_role_service.recent_chat_list(
        user, current_language, offset=offset, limit=limit
    )
    return response_util.success(
        {
            "total": count,
            "list": ret,
        }
    )


@chat_api_router.get("/chat/conversation/list")
@limit_requests_by_tg(prefix="chat:conversation:list", expire=5)
async def chat_conversation(
    request: Request,
    mode_target_id: int,
    mode_type=ChatModeType.SINGLE.value,
    user_id: int = Depends(get_current_user),
    current_language: str = Header(default=Language.ZH.value),
):
    cov_list = await chat_message_service.list_conversation(
        user_id, mode_type, mode_target_id, current_language
    )
    cov_list = cov_list[:3] if len(cov_list) > 3 else cov_list
    # active_vip = await vip_service.active_vip(user_id)
    # if not active_vip:
    #     # cov_list 第3个元素开始，read设置为False
    #     for i in range(3, len(cov_list)):
    #         cov_list[i].has_permission = False
    #         cov_list[i].conversation_id = "**********"
    #         cov_list[i].desc = "VIP可读取"
    return response_util.ok({"list": cov_list})


# @chat_api_router.get("/history")
@chat_api_router.get("/chat/history")
@limit_requests_by_tg(prefix="chat:history", expire=5)
async def history(
    request: Request,
    mode_type=ChatModeType.SINGLE.value,
    role_id: int = 0,
    group_id: int = 0,
    conversation_id: str = "",
    new_start: int = 0,
    user_snapshot_id: int = 0,
    user_id: int = Depends(get_current_user),
    current_language: str = Header(default=Language.ZH.value),
):
    history_req = HistoryRequest(
        mode_type=mode_type,
        group_id=group_id,
        role_id=role_id,
        conversation_id=conversation_id,
        new_start=new_start,
        language=current_language,
    )
    log.info(f"ChatHistory user_id:{user_id},request: {history_req}")
    if not history_req.verify_param():
        return response_util.json_param_error("param error")
    user = await user_service.get_user_by_id(user_id)
    error = await role_access_service.verify_history_request(history_req, user)
    if error:
        log.error(f"history param error,user_id:{user_id},request: {history_req}")
        return error
    if history_req.new_start == 0 and not history_req.conversation_id:
        history_req.conversation_id = await chat_message_service.latest_cov_id(
            user_id, history_req
        )
        if not history_req.conversation_id:
            history_req.new_start = 1

    user_snapshot = None
    conv_id = history_req.conversation_id
    if new_start:
        user_snapshot = await chat_snapshot_service.get_user_role_snapshot_by_id(
            user_snapshot_id
        )
        history_req = await chat_message_service.init_first_history_message(
            user, history_req, ChatPlatform.TMA.value, user_snapshot
        )
        conv_id = history_req.conversation_id
    if new_start and user_snapshot and conv_id:
        user_snapshot = await chat_snapshot_service.create_urs_relation(
            user, role_id, conv_id, user_snapshot_id
        )
    if not user_snapshot:
        user_snapshot = await chat_snapshot_service.get_user_role_snapshot_by_conv_id(
            user.id, conv_id
        )
    history_response = await chat_message_service.list_user_history(
        user, history_req, user_snapshot
    )
    return history_response


# 聊天时更新模型
@chat_api_router.post("/chat/update_model")
async def update_chat_model_during_chat(
    request: UpdateModelRequest,
    user_id: int = Depends(get_current_user),
    current_language: str = Header(default=Language.ZH.value),
):
    log.info(f"ChatUpdateModel user_id:{user_id},request: {request}")
    user = await user_service.get_user_by_id(user_id)
    current_model_product = await product_service.get_user_chat_product(user)
    if not current_model_product:
        current_model_product = await product_service.get_default_chat_product()
    if (
        current_model_product.mid == request.model
        and user.chat_channel == request.chat_channel
    ):
        log.info(f"model not changed, user_id:{user_id}")
        return response_util.ok({"result": True})

    target_product = await product_service.get_original_chat_product_by_mid(
        request.model
    )
    if not target_product:
        log.error(f"target product not found, user_id:{user_id}")
        raise exception_util.verify_exception(message="模型不存在或已下架")
    # 检查用户是否有权限使用该模型
    if not await role_access_service.allow_model_by_product(target_product, user):
        raise exception_util.verify_exception(
            error_code=ErrorCode.UN_SUPPORT_MODEL.value,
            error_key=ErrorKey.MODEL_ONLY_FOR_PAID_USER.value,
        )
    # 查找对应的msg id
    last_message_id = await chat_message_service.get_last_message_id_by_conv_id(
        user_id, request.conversation_id
    )
    # if not last_message_id:
    # raise exception_util.verify_exception(message="会话不存在或消息已被删除")

    # 更新用户的模型配置
    user.chat_product_mid = target_product.mid
    user.chat_channel = request.chat_channel
    await user_service.update_user(user)
    # 记录模型切换历史
    await chat_model_switch_history.add_model_switch_history(
        user_id,
        request.mode_type.value,
        request.mode_target_id,
        request.conversation_id,
        last_message_id,
        request.event_type.value,
        current_model_product.mid,
        current_model_product.display_name,
        target_product.mid,
        target_product.display_name,
        from_chat_channel=request.from_chat_channel,
        to_chat_channel=request.to_chat_channel,
    )

    return response_util.ok({"result": True})


@chat_api_router.post("/del_message")
async def del_message(
    request: DelMessageRequest, user_id: int = Depends(get_current_user)
):
    log.info(f"del_message user_id:{user_id},request: {request}")
    request.message_ids = [x for x in request.message_ids if x]
    return await chat_message_service.delete_message(user_id, request.message_ids)


@chat_api_router.post("/chat/user_input")
async def user_input(
    payload: ChatRequest,
    user_id: int = Depends(get_current_user),
    current_language: str = Header(default=Language.ZH.value),
):
    payload.language = current_language
    log.info(f"ChatUserInput user_id:{user_id},request: {payload}")
    if not payload.verify_param() or not payload.message or not payload.conversation_id:
        return response_util.error(
            ERROR_CODE.PARAM_ERROR.value, "参数错误（请联系客服）"
        )
    user = await user_service.get_user_by_id(user_id)
    chat_next_input = await user_chat_service.build_chat_input_param(
        user_id, payload, current_language
    )
    # error = await role_access_service.verify_user_input(user, payload)
    # if error:
    #     return error
    history = await chat_message_service.save_user_message(
        user, chat_next_input, payload
    )
    human_message_id = history.message_id
    return UserInputResponse(
        human_message_id=human_message_id,
        version=str(history.version),
    )


@chat_api_router.post("/chat/user_input/v1")
@limit_requests_by_tg(prefix="chat:user_input:v1", expire=5)
async def user_input_v1(
    request: Request,
    payload: ChatRequest,
    user_id: int = Depends(get_current_user),
    current_language: str = Header(default=Language.ZH.value),
):
    payload.language = current_language
    log.info(f"ChatUserInput user_id:{user_id},request: {payload}")
    if not payload.verify_param() or not payload.message or not payload.conversation_id:
        return response_util.error(
            ERROR_CODE.PARAM_ERROR.value, "参数错误（请联系客服）"
        )
    if redis_client.exit_chat_lock(user_id):
        raise exception_util.verify_exception(message="AI消息回复未完成，请稍后再试")

    user = await user_service.get_user_by_id(user_id)
    input = await user_chat_service.build_chat_input_param(
        user_id, payload, current_language
    )
    error = await role_access_service.verify_user_input(user, payload, input)
    if error:
        return error
    history = await chat_message_service.save_user_message(user, input, payload)
    human_message_id = history.message_id
    return response_util.ok(
        {
            "human_message_id": human_message_id,
            "version": str(history.version),
        }
    )


@chat_api_router.post("/chat")
@chat_api_router.post("/chat/replay")
async def chat_new(
    request: Request,
    payload: ChatRequest,
    user_id: int = Depends(get_current_user),
    current_language: str = Header(default=Language.ZH.value),
):
    payload.language = current_language
    input = await user_chat_service.build_chat_input_param(
        user_id, payload, current_language
    )
    user = await user_service.get_user_by_id(user_id)
    log.info(f"ChatReplay user_id:{user_id},request: {payload}")

    verify_ret = await role_access_service.verify_chat(user, payload, input)
    if verify_ret:
        return verify_ret

    try:
        human_message_id = ""
        if not payload.isRetry and len(payload.message) > 0:
            history = await chat_message_service.save_user_message(user, input, payload)
            human_message_id = history.message_id
            input.human_message_id = human_message_id

        input = await chat_message_service.build_model_config(
            input, Scenario.CHAT.value
        )
        input = await chat_message_service.build_request_history(input)

        input = await chat_message_service.build_character_book(input)

        input = await chat_message_service.build_user_custom_persona(input)
        if await request.is_disconnected():
            log.error(f"chat_new disconnected user_id:{user_id},request: {payload}")
            return response_util.chat_error_message("disconnected")

        latest_message = await chat_message_service.check_interrupt_message(input)
        if latest_message:
            log.warning(f"chat_new interrupt user_id:{user_id},request: {payload}")
            input.version = latest_message.version
            input.message_id = latest_message.message_id
            return response_util.chat_success(latest_message, input.get_headers())

        if not redis_client.chat_lock(user_id):
            return response_util.chat_repeat_response(current_language)
        chat_response = await chat_message_service.llm_call_auth_retry(input)
        if not chat_response.success or not chat_response.response:
            redis_client.chat_lock_release(user_id)
            return response_util.chat_error_response(current_language, need_retry=True)

        history_iter, response_iter = aioitertools.tee(chat_response.response)

        @redis_client.async_release_lock(key="chat", id=str(user_id))
        async def history_task():
            await message_service.save_new_history_content_v2(
                user,
                payload,
                input,
                input_token_sum=chat_response.token_sum,
                first_chunk=chat_response.first_chunk,
                response_iter=history_iter,
            )
            input.save_finished = True

        asyncio.create_task(history_task())
        headers = {
            "Conversation-Id": payload.conversation_id,
            "Message-Id": input.message_id,
            "Human-Message-Id": human_message_id,
            "Message-Version": str(input.version),
        }
        content_func = message_service.chat_result_iter_v5(
            input, response_iter, chat_response.first_chunk
        )
        return EventSourceResponse(content=content_func, headers=headers)
    except Exception as e:
        await chat_result_service.error_handler(input, e)
        redis_client.chat_lock_release(user_id)
        return response_util.chat_error_response(current_language, need_retry=True)


@chat_api_router.post("/impersonate")
async def impersonate(
    request: Request,
    req: ImpersonateRequest,
    current_language: str = Header(default=Language.ZH.value),
    user_id: int = Depends(get_current_user),
):
    req.language = current_language
    log.info(f"ChatImpersonate user_id:{user_id},request: {req}")
    user = await user_service.get_user_by_id(user_id)
    role_id = await chat_message_service.impersonate_def_role_id(req)
    chat_req = ChatRequest(
        mode_type=req.mode_type,
        group_id=req.group_id,
        role_id=role_id,
        conversation_id=req.conversation_id,
        language=current_language,
        api_version=req.api_version,
    )
    input = await user_chat_service.build_chat_input_param(
        user_id, chat_req, current_language
    )
    input = await chat_message_service.build_model_config(
        input, Scenario.IMPERSONATE.value
    )
    input = await chat_message_service.build_impersonate_request_history(input)

    input = await chat_message_service.build_character_book(input)
    input = await chat_message_service.build_user_custom_persona(input)
    verify_ret = await role_access_service.verify_impersonate_chat(user, req, input)
    if verify_ret:
        return verify_ret
    if not redis_client.chat_lock(user_id, "impersonate"):
        return response_util.chat_repeat_response(current_language)
    try:
        chat_response = await chat_message_service.llm_call_auth_retry(input)
        if not chat_response.success or not chat_response.response:
            redis_client.chat_lock_release(user_id, "impersonate")
            return response_util.chat_error_response(
                current_language, ErrorKey.CHAT_SYS_ERR.value, False
            )
        history_iter, response_iter = aioitertools.tee(chat_response.response)

        @redis_client.async_release_lock(key="impersonate", id=str(user_id))
        async def single_task():
            await message_service.save_impersonate_history_new_v1(
                history_iter, chat_response.token_sum, input, chat_response.first_chunk
            )

        asyncio.create_task(single_task())
        return EventSourceResponse(
            content=message_service.chat_result_iter_v5(
                input, response_iter, chat_response.first_chunk
            ),
        )
    except Exception as e:
        log.error(f"impersonate error:{e}")
        redis_client.chat_lock_release(user_id)
        return response_util.chat_error_response(
            current_language, ErrorKey.CHAT_SYS_ERR.value, True
        )


# # 测试专用接口，去掉各种限制
# @chat_api_router.post("/chat/replay_test")
# async def replay_test(
#     payload: ChatRequest,
#     user_id: int = 0,
#     current_language: str = Header(default=Language.ZH.value),
# ):
#     payload.language = current_language
#     user = await user_service.get_user_by_id(user_id)

#     log.info(f"chat user_id:{user_id},request: {payload}")
#     input = await chat_message_service.build_chat_input_param(
#         user_id, payload, current_language
#     )
#     try:
#         human_message_id = ""
#         if not payload.isRetry and len(payload.message) > 0:
#             history = await chat_message_service.save_user_message(user, input, payload)
#             human_message_id = history.message_id
#             input.human_message_id = human_message_id

#         input = await chat_message_service.build_model_config(
#             input, Scenario.CHAT.value
#         )
#         input = await chat_message_service.build_request_history(input)

#         input = await chat_message_service.build_character_book(input)

#         chat_response = await lite_llm_bot.role_next(input)
#         history_iter, response_iter = aioitertools.tee(
#             chat_response.response.fetch_sync_stream()
#         )

#         async def history_task():
#             await message_service.save_new_history_v1(
#                 user, payload, input, history_iter, chat_response.token_sum
#             )

#         asyncio.create_task(history_task())
#         headers = {
#             "Conversation-Id": payload.conversation_id,
#             "Message-Id": input.message_id,
#             "Human-Message-Id": human_message_id,
#             "Message-Version": str(input.version),
#         }
#         return EventSourceResponse(
#             content=message_service.chat_result_iter_v4(input, response_iter),
#             headers=headers,
#         )
#     except Exception as e:
#         log.error(f"chat_new error:{e}")
#         redis_client.chat_lock_release(user_id)
#         return response_util.chat_error_response(current_language)

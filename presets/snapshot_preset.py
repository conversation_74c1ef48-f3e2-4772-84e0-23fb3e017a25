from common.common_constant import PresetReplace


GENERATE_SCENARIO_SYSTEM_PRESET = """# 你是一个虚拟角色，拥有丰富的背景故事和个性。你的任务是根据用户提供的聊天记录
## 生成场景内容使用<response>标签包裹，场景需要和聊天内容有一定的相关性，并且基于场景进行一些发挥。场景内容需要包含角色的行为、环境描述等

整体返回格式如下：
<response>
场景内容
</response>
"""

GENERATE_SCENARIO_USER_PRESET = f"""首先会提供给人物聊天信息：
<chat_history>
{PresetReplace.CHAT_HISTORY.value}
</chat_history>
"""
GENERATE_SCENARIO_AI_PRESET = f"""明白，我是虚拟角色，我会根据聊天记录生成一个聊天场景，生成的场景内容将使用<response>标签包裹，内容如下："""


GENERATE_DESCRIPTION_SYSTEM_PRESET = """你是一个虚拟角色，拥有丰富的背景故事和个性。你的任务是根据用户提供的聊天记录，基于角色原有描述，将聊天中的重点事项，发生的事件与结果，加入到原有的角色描述中
"""
GENERATE_DESCRIPTION_USER_PRESET = f"""首先会提供给原有的角色信息：
<description>
{PresetReplace.CHAR_DESCRIPTION.value}
</description>
其次提供聊天内容：
<chat_history>
{PresetReplace.CHAT_HISTORY.value}
</chat_history>
请根据原有的角色信息和聊天内容，生成一个新的角色描述，包含原有的角色描述，以及聊天中的重点事项，生成的内容使用<response>标签包裹。
"""
GENERATE_DESCRIPTION_AI_PRESET = f"""明白，我是虚拟角色，我会根据聊天记录生成一个角色描述，生成的内容将使用<response>标签包裹，内容如下："""


SYSTEM_PRESET = """你是一个有很强沟通与总结能力的情场大师，需要你根据一段聊天记录做总结与分析，总结提供的的聊天信息，记住{{char}}与{{user}}之间发生的重点事项，总结{{char}}的各种细节，生成一个聊天场景要求与{{char}}最新的回复
## 聊天场景要求
1、需要包括核心信息：参与的角色信息、所处的环境、时间、地点、事件的起因、经过和结果，以及重点互动事项
2、需要包括角色的行为、环境描述，以及角色的心理活动等

## {{char}}最新回复要求
1、需要以{{char}}口吻向{{user}}说，继续续写之前聊天
2、简单总结一下之前发生了什么事情，并总结之前发生事项，比如：场景转换（如果有）、新事件、场景、动作、对话、想法和意象等，并加入到{{char}}最新回复中
3、将第一人称“我”改为{{char}}
"""

USER_PRESET = f"""
首先会提供给人物聊天信息：
<chat_history>
{PresetReplace.CHAT_HISTORY.value}
</chat_history>
请根据聊天记录生成一个聊天场景，内容使用<scenario>标签包裹，{"{{char}}"}最新回复使用<first_message>标签包裹
"""

AI_PRESET = """明白，我是虚拟角色，我会总结聊天信息生成聊天场景与{{char}}最新回复，生成的内容将使用<scenario>标签包裹，{{char}}最新回复使用<first_message>标签包裹，内容如下："""

from datetime import UTC, datetime, timedelta
import logging
from typing import List, <PERSON><PERSON>

from pydantic import BaseModel
from common.common_constant import RechargeRouteStrategy
from persistence.redis_client import redis_client
from persistence.models.models import (
    RechargeChannelEnum,
    RechargeChannelConfig,
    RechargeChannelStats,
    RechargeChannelControl,
    RechargeChannelWhitelist,
    RechargeOrder,
    RechargeStatusEnum
)

all_channels = [
    RechargeChannelEnum.TMPAY, 
    # RechargeChannelEnum.FFPAY,
    # RechargeChannelEnum.QSZF,
    RechargeChannelEnum.SJZF,
    RechargeChannelEnum.JLBZF,
    RechargeChannelEnum.XJTZF,
    RechargeChannelEnum.TTZF,
    # RechargeChannelEnum.SDFKW_API,
]


class RechargeChannelRatio(BaseModel):
    channel: str
    ratio: int
    current: int

async def get_configs() -> list[RechargeChannelConfig]:
    return await RechargeChannelConfig.all()

async def add_config(config: RechargeChannelConfig):
    await config.save()

async def update_config(config: RechargeChannelConfig): 
    await config.save()

async def get_by_product_id(product_id: str) -> RechargeChannelConfig | None:
    return await RechargeChannelConfig.get_or_none(recharge_product_id=product_id)

# 定义通道状态
CHANNEL_STATUS_AVAILABLE = "available"
CHANNEL_STATUS_UNAVAILABLE = "unavailable"
CHANNEL_STATUS_RETRIABLE = "retriable"

# Redis键前缀
REDIS_CHANNEL_STATUS_KEY_PREFIX = "recharge:channel:status:"
REDIS_CHANNEL_RETRY_TIME_KEY_PREFIX = "recharge:channel:retry_time:"

async def get_current_mode() -> RechargeRouteStrategy:
    cm = redis_client.get("recharge_route_strategy")
    if not cm:
        return RechargeRouteStrategy.MANUAL
    cm = cm.decode()
    return RechargeRouteStrategy(cm)

def get_all_can_use_channels() -> list[RechargeChannelEnum]:
    channels = []
    for channel in all_channels:
        status = get_channel_status(channel)
        if status != CHANNEL_STATUS_UNAVAILABLE:
            channels.append(channel)
    if len(channels) == 0:
        channels = all_channels
    return channels

def set_channel_status(channel: str, status: str):
    key = f"{REDIS_CHANNEL_STATUS_KEY_PREFIX}{channel}"
    redis_client.set(key, status)

def get_channel_status(channel: str):
    key = f"{REDIS_CHANNEL_STATUS_KEY_PREFIX}{channel}"
    status = redis_client.get(key)
    return status.decode('utf-8') if status else CHANNEL_STATUS_AVAILABLE

def set_channel_retry_time(channel: str, retry_time: datetime):
    key = f"{REDIS_CHANNEL_RETRY_TIME_KEY_PREFIX}{channel}"
    timestamp = retry_time.timestamp()
    redis_client.set(key, str(timestamp))

def get_channel_retry_time(channel: str):
    key = f"{REDIS_CHANNEL_RETRY_TIME_KEY_PREFIX}{channel}"
    timestamp = redis_client.get(key)
    if timestamp:
        return datetime.fromtimestamp(float(timestamp.decode('utf-8')), UTC)
    return None

async def update_channel_success_rate(channel: str):
    """更新渠道成功率统计 - 按支付类型分别统计"""
    two_four_hours_ago = datetime.now(UTC) - timedelta(hours=2)

    pay_types_to_stats = ['wechat', 'alipay', 'ALL']

    updated_stats = []

    for pay_type in pay_types_to_stats:
        if pay_type == 'ALL':
            # 统计所有订单
            total_orders_list = await RechargeOrder.filter(
                recharge_channel=channel,
                created_at__gte=two_four_hours_ago,
                pay_fee__gt=0
            ).order_by('-id').limit(12)

        else:
            # 统计特定支付类型的订单
            total_orders_list = await RechargeOrder.filter(
                recharge_channel=channel,
                pay_type=pay_type,
                created_at__gte=two_four_hours_ago,
                pay_fee__gt=0
            ).order_by('-id').limit(12)

        success_orders = len([o for o in total_orders_list if o.status == RechargeStatusEnum.SUCCEED])
        total_orders = len(total_orders_list)
        success_rate = (success_orders / total_orders * 100) if total_orders > 0 else 0.0

        # 更新或创建统计记录
        stats, created = await RechargeChannelStats.get_or_create(
            channel=channel,
            pay_type=pay_type,
            defaults={
                'total_orders': total_orders,
                'success_orders': success_orders,
                'success_rate': success_rate,
                'pay_type': pay_type
            }
        )

        if not created:
            stats.total_orders = total_orders
            stats.success_orders = success_orders
            stats.success_rate = success_rate
            stats.pay_type = pay_type
            await stats.save()

        updated_stats.append(stats)

    return updated_stats


async def get_manual_strategy_channels(pay_type: str, amount: int): 
    return await get_enabled_controls(pay_type, amount)

async def get_channel_queues_for_routing(queue_type: str, pay_type: str = 'alipay', amount: int = 0) -> Tuple[List[str], List[str]]:
    specific_stats = await RechargeChannelStats.filter(pay_type=pay_type).all()

    stats_map = {}
    for stat in specific_stats:
        stats_map[stat.channel] = stat

    enabled_channels = await get_enabled_channels_from_controls(pay_type, amount)

    # 为启用的渠道构建统计数据
    channel_stats = []
    for channel in enabled_channels:
        if channel in stats_map:
            stat = stats_map[channel]
            channel_stats.append((channel.value, stat.success_rate))
        else:
            # 新渠道默认成功率为50%
            channel_stats.append((channel.value, 50.0))

    # 按成功率从高到低排序
    channel_stats.sort(key=lambda x: x[1], reverse=True)

    # 构建第一个队列：成功率 >= 40% 的渠道
    high_success_channels = [ch for ch, rate in channel_stats if rate >= 40.0]

    # 构建第二个队列：从排名第二位开始，包括成功率 < 40% 的渠道
    low_success_channels = [ch for ch, rate in channel_stats if rate < 40.0]
    if len(high_success_channels) > 1:
        # 从第二位开始的高成功率渠道 + 低成功率渠道
        secondary_queue = high_success_channels[1:] + low_success_channels
    else:
        secondary_queue = low_success_channels

    if queue_type == 'type1':
        return high_success_channels, secondary_queue
    else:  # type2
        return secondary_queue, high_success_channels


async def filter_channels_by_user_history(channels: List[str], user_id: int) -> List[str]:
    from services import recharge_service

    unpaid_channels = await recharge_service.get_user_unpaid_channels_in_last_10_minutes(user_id)

    # 过滤掉用户近期未支付的渠道
    filtered_channels = [ch for ch in channels if ch not in unpaid_channels]

    # 如果所有渠道都被过滤掉了，返回原始列表（避免无渠道可用）
    return filtered_channels if filtered_channels else channels

async def get_channel_controls() -> List[RechargeChannelControl]:
    """获取所有渠道控制配置"""
    return await RechargeChannelControl.all()


async def add_channel_control(control: RechargeChannelControl) -> RechargeChannelControl:
    """添加渠道控制配置"""
    await control.save()
    return control


async def update_channel_control(control: RechargeChannelControl) -> RechargeChannelControl:
    """更新渠道控制配置"""
    await control.save()
    return control


async def delete_channel_control(control_id: int) -> bool:
    """删除渠道控制配置"""
    try:
        control = await RechargeChannelControl.get(id=control_id)
        await control.delete()
        return True
    except:
        return False


async def get_channel_control_by_id(control_id: int) -> RechargeChannelControl | None:
    """根据ID获取渠道控制配置"""
    return await RechargeChannelControl.get_or_none(id=control_id)

async def get_enabled_controls(pay_type: str, amount: int) -> List[RechargeChannelControl]:
    controls = await RechargeChannelControl.filter(
        pay_type=pay_type,
        enabled=True
    ).all()

    return [control for control in controls
        if (
            (control.min_amount * 100000 < amount < control.max_amount * 100000) 
            and control.enabled 
        )
    ]

async def get_enabled_channels_from_controls(pay_type: str, amount: int) -> List[RechargeChannelEnum]:
    controls = await get_enabled_controls(pay_type, amount)
    return [c.channel for c in controls]

async def is_channel_enabled_for_request(channel: RechargeChannelEnum, pay_type: str, amount: int) -> bool:
    # 使用优化后的批量查询方法
    enabled_channels = await get_enabled_channels_from_controls(pay_type, amount)
    return channel in enabled_channels

async def get_enabled_channels_for_request(pay_type: str, amount: int) -> List[str]:
    enabled_channels = await get_enabled_channels_from_controls(pay_type, amount)
    return [channel.value for channel in enabled_channels]

async def initialize_default_channel_controls():
    """初始化默认的渠道控制配置"""
    channels = [
        RechargeChannelEnum.TMPAY,
        RechargeChannelEnum.FFPAY,
        RechargeChannelEnum.QSZF,
        RechargeChannelEnum.SJZF,
        RechargeChannelEnum.JLBZF,
        RechargeChannelEnum.XJTZF,
        RechargeChannelEnum.SDFKW_API,
    ]

    pay_types = ['alipay', 'wechat']

    for channel in channels:
        for pay_type in pay_types:
            # 检查是否已存在配置
            existing = await RechargeChannelControl.get_or_none(
                channel=channel,
                pay_type=pay_type,
                min_amount=0,
                max_amount=0
            )

            if not existing:
                control = RechargeChannelControl(
                    channel=channel,
                    pay_type=pay_type,
                    min_amount=0,
                    max_amount=0,
                    enabled=True,
                    priority=100,
                    remark=f"默认配置 - {channel.description} - {pay_type}"
                )
                await control.save()

async def get_channel_queue_item(pay_type: str) -> str:
    queue_key = f"recharge:channel:queue:{pay_type}"
    for _ in range(len(all_channels)):
        channel = redis_client.rpoplpush(queue_key, queue_key)
        if channel:
            cv = channel.decode('utf-8')
            if cv in all_channels:
                return cv

    logging.warning(f"No available channel in queue for pay_type: {pay_type}")
    return ""

async def generate_channel_queue():
    for pay_type in ['alipay', 'wechat']:
        controls = await RechargeChannelControl.filter(
            pay_type=pay_type,
            enabled=True
        ).all()
        nodes = [RechargeChannelRatio(channel=control.channel.value, ratio=control.ratio, current=0) for control in controls]
        queue = []
        total_weight = sum(control.ratio for control in controls)
        for _ in range(100):
            for n in nodes:
                n.current += n.ratio
            chosen = max(nodes, key=lambda n: n.current)
            queue.append(chosen.channel)
            chosen.current -= total_weight

        logging.info(f"Generated queue for {pay_type}: {queue}")
        with redis_client.pipeline() as pipe:
            pipe.delete(f"recharge:channel:queue:{pay_type}")
            pipe.rpush(f"recharge:channel:queue:{pay_type}", *queue)
            pipe.execute()

async def get_user_whitelist_channel(tg_id: int) -> RechargeChannelWhitelist | None:
    return await RechargeChannelWhitelist.filter(tg_id=tg_id, enabled=True).first()

async def add_whitelist_entry(tg_id: int, channel: RechargeChannelEnum, remark: str = "") -> RechargeChannelWhitelist:
    entry, created = await RechargeChannelWhitelist.get_or_create(
        tg_id=tg_id,
        channel=channel,
        defaults={
            'enabled': True,
            'remark': remark
        }
    )
    if not created:
        entry.enabled = True
        entry.remark = remark
        await entry.save()
    return entry


async def remove_whitelist_entry(tg_id: int, channel: RechargeChannelEnum) -> bool:
    entry = await RechargeChannelWhitelist.get_or_none(tg_id=tg_id, channel=channel)
    if entry:
        entry.enabled = False
        await entry.save()
        return True
    return False

async def get_whitelist_entries() -> List[RechargeChannelWhitelist]:
    return await RechargeChannelWhitelist.all()


async def get_whitelist_by_channel(channel: RechargeChannelEnum) -> List[RechargeChannelWhitelist]:
    return await RechargeChannelWhitelist.filter(channel=channel, enabled=True).all()

async def update_whitelist_entry(entry_id: int, enabled: bool | None = None, remark: str = '') -> RechargeChannelWhitelist:
    entry = await RechargeChannelWhitelist.get(id=entry_id)
    if enabled is not None:
        entry.enabled = enabled
    if remark is not None:
        entry.remark = remark
    await entry.save()
    return entry

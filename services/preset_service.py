import copy
import os

# from persistence.presets import Model
from common.common_constant import UserModelFilter
from common.models.chat_model import ChatNextInput
from persistence.models.models import ModelWaterConfig, Product
from services import config_service


# async def select_model_v2(message_models: list[str], input: ChatNextInput) -> str:
#     user_model = input.llm_model
#     base_waters = await config_service.list_water_config_by_model_and_use_filter(
#         user_model, UserModelFilter.DEFAULT.value
#     )
#     free_waters = await config_service.list_water_config_by_model_and_use_filter(
#         user_model, UserModelFilter.FREE_BENEFIT.value
#     )
#     free_waters = [x for x in free_waters if x.num > 0]
#     if input.user_model_filter == UserModelFilter.FREE_BENEFIT.value and free_waters:
#         waters = free_waters
#     else:
#         waters = base_waters

#     def model_select():
#         if len(waters) == 1:
#             return waters[0].to_llm_model
#         water_copy = copy.deepcopy(waters)
#         water_copy = [x for x in water_copy if x.num > 0]
#         if not water_copy:
#             return waters[0].to_llm_model
#         if len(water_copy) == 1:
#             return water_copy[0].to_llm_model

#         min_water = water_copy[-1]
#         water_copy.remove(min_water)
#         if min_water.num > 1 and len(
#             [x for x in water_copy if x.num % min_water.num == 0]
#         ) == len(water_copy):
#             for x in water_copy:
#                 x.num = x.num // min_water.num
#             min_water.num = 1
#         mid = {water.to_llm_model: water for water in water_copy}
#         # 反向遍历model_list
#         for i in range(len(message_models) - 1, -1, -1):
#             if min_water.to_llm_model == message_models[i]:
#                 min_water.num -= 1 if min_water.num > 0 else 0
#             if min_water.num == 0:
#                 break
#             if message_models[i] not in mid:
#                 continue
#             mid_model = mid[message_models[i]].to_llm_model
#             mid[mid_model].num = mid[mid_model].num - 1 if mid[mid_model].num > 0 else 0
#         mid = {k: v for k, v in mid.items() if v.num > 0}
#         if len(mid) == 0:
#             return min_water.to_llm_model
#         mid = sorted(mid.values(), key=lambda x: x.num * 100000 - x.id, reverse=True)
#         if (
#             len(mid) >= 3
#             and message_models
#             and message_models[-1] == mid[0].to_llm_model
#         ):
#             return mid[1].to_llm_model
#         return mid[0].to_llm_model

#     ret_to_llm_model = model_select()
#     if input.auto_retry and waters:
#         ready_model_list = [
#             x.to_llm_model for x in waters if x.to_llm_model != ret_to_llm_model
#         ]
#         if ready_model_list:
#             return ready_model_list[0]
#     return ret_to_llm_model


async def select_model_v3(
    message_models: list[str], input: ChatNextInput
) -> ModelWaterConfig:
    user_model = input.llm_model
    base_waters = await config_service.list_water_config_by_model_and_use_filter(
        user_model, UserModelFilter.DEFAULT.value
    )
    free_waters = await config_service.list_water_config_by_model_and_use_filter(
        user_model, UserModelFilter.FREE_BENEFIT.value
    )
    free_waters = [x for x in free_waters if x.num > 0]
    if input.user_model_filter == UserModelFilter.FREE_BENEFIT.value and free_waters:
        waters = free_waters
    else:
        waters = base_waters

    def model_select():
        if len(waters) == 1:
            return waters[0]
        water_copy = copy.deepcopy(waters)
        water_copy = [x for x in water_copy if x.num > 0]
        if not water_copy:
            return waters[0]
        if len(water_copy) == 1:
            return water_copy[0]

        min_water = water_copy[-1]
        water_copy.remove(min_water)
        if min_water.num > 1 and len(
            [x for x in water_copy if x.num % min_water.num == 0]
        ) == len(water_copy):
            for x in water_copy:
                x.num = x.num // min_water.num
            min_water.num = 1
        mid = {water.to_llm_model: water for water in water_copy}
        # 反向遍历model_list
        for i in range(len(message_models) - 1, -1, -1):
            if min_water.to_llm_model == message_models[i]:
                min_water.num -= 1 if min_water.num > 0 else 0
            if min_water.num == 0:
                break
            if message_models[i] not in mid:
                continue
            mid_model = mid[message_models[i]].to_llm_model
            mid[mid_model].num = mid[mid_model].num - 1 if mid[mid_model].num > 0 else 0
        mid = {k: v for k, v in mid.items() if v.num > 0}
        if len(mid) == 0:
            return min_water
        mid = sorted(mid.values(), key=lambda x: x.num * 100000 - x.id, reverse=True)
        if (
            len(mid) >= 3
            and message_models
            and message_models[-1] == mid[0].to_llm_model
        ):
            return mid[1]
        return mid[0]

    ret_to_llm_model = model_select()
    if input.auto_retry and ret_to_llm_model.backup_to_llm_models:
        return ret_to_llm_model
    if input.auto_retry and len(waters) > 1:
        waters = [x for x in waters if x.to_llm_model != ret_to_llm_model]
        ret_to_llm_model = model_select()
    return ret_to_llm_model

# async def select_model_v3(message_models: list[str], input: ChatNextInput) -> str:
#     user_model = input.llm_model
#     waters = await config_service.list_water_config_by_model(user_model)
#     if not waters:
#         return user_model
#     base_waters = [
#         x
#         for x in waters
#         if not x.use_filters or UserModelFilter.DEFAULT.value in x.use_filters
#     ]
#     if input.user_model_filters:
#         new_waters = [
#             x
#             for x in waters
#             if x.use_filters
#             and any(x in input.user_model_filters for x in x.use_filters)
#         ]
#         waters = new_waters
#     waters = waters if waters else base_waters
#     # 避免排序不稳定
#     waters.sort(key=lambda x: x.num * 100000 - x.id, reverse=True)

#     def model_select():
#         if len(waters) == 1:
#             return waters[0].to_llm_model
#         water_copy = copy.deepcopy(waters)
#         water_copy = [x for x in water_copy if x.num > 0]
#         if not water_copy:
#             return waters[0].to_llm_model
#         if len(water_copy) == 1:
#             return water_copy[0].to_llm_model

#         min_water = water_copy[-1]
#         # 可以整除
#         all_divisible = bool(all(x.num % min_water.num == 0 for x in water_copy))
#         if min_water.num > 1 and all_divisible:
#             for x in water_copy:
#                 x.num = x.num // min_water.num
#         total_weight = sum(x.num for x in water_copy)
#         mid_water_map = {water.to_llm_model: water for water in water_copy}
#         mid_models = (
#             message_models
#             if len(message_models) < total_weight
#             else message_models[-(total_weight - 1) :]
#         )
#         for model in mid_models:
#             if model in mid_water_map:
#                 mid_water_map[model].num -= 1 if mid_water_map[model].num > 0 else 0
#         valid_waters = [x for x in mid_water_map.values() if x.num > 0]
#         valid_waters.sort(key=lambda x: x.num * 100000 - x.id, reverse=True)
#         if len(valid_waters) > 1 and message_models[-1] == valid_waters[0].to_llm_model:
#             return valid_waters[1].to_llm_model
#         return valid_waters[0].to_llm_model

#     ret_to_llm_model = model_select()
#     if input.isRetry and input.auto_retry and waters:
#         message_models.append(ret_to_llm_model)
#         ret_to_llm_model = model_select()
#     return ret_to_llm_model

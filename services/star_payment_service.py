from tortoise.transactions import in_transaction
from aiogram.types import LabeledPrice, SuccessfulPayment, StarTransaction, TransactionPartnerUser
from persistence.models.models import RechargeOrder, RechargeProduct, RechargeStatusEnum, StarPaymentOrder, RechargeChannelEnum, TgBotConfig
from services.out_recharge_common import create_recharge_order_with_product, pay_success
from services.user_service import user_service
from services import re_purchase_service, recharge_service, bot_services, user_growth_service
from utils import env_util

async def create_star_payment_order_with_product(
        user_id: int, tg_user_id: int, recharge_product: RechargeProduct, 
        bot_config: TgBotConfig) -> tuple[StarPaymentOrder | None, list[LabeledPrice]]:
    bot_id = bot_config.bot_id
    user = await user_service.get_user_by_id(user_id)
    if user is None:
        return None, []

    recharge_order = await create_recharge_order_with_product(
        user_id, recharge_product, RechargeChannelEnum.STAR_PAYMENT, bot_config.bot_id)
    
    if recharge_order is None:
        return None, []

    if bot_config.en_bot():
        star_amount = int(recharge_product.price / 100000 * 50)
    else:
    # 价格：0.99 美元 50 star，到账 0.65 美元 p / 1000000 * 50 / 0.65
        star_amount = int(recharge_product.price / 100000 * 50 / 0.65)
    if not env_util.is_prod_env():
        star_amount = 1

    star_payment_order = StarPaymentOrder(
        user_id=user_id,
        bot_id=bot_id,
        chat_id=tg_user_id,
        star_amount=star_amount,
        recharge_order_id=recharge_order.recharge_order_id
    )
    await star_payment_order.save()
    prices = [LabeledPrice(label=recharge_product.title, amount=star_amount)]

    return star_payment_order, prices

async def create_star_payment_order(user_id: int,
                                    tg_user_id: int,
                                    recharge_product_id: str,
                                    bot_config: TgBotConfig) -> tuple[StarPaymentOrder | None, list[LabeledPrice]]:
    recharge_product = await recharge_service.get_recharge_product(recharge_product_id)
    if recharge_product is None:
        return None, []
    return await create_star_payment_order_with_product(
        user_id, tg_user_id, recharge_product, bot_config)

async def get_star_payment_order(invoice_id: str, chat_id: int) -> StarPaymentOrder | None:
    return await StarPaymentOrder.filter(
        invoice_id=invoice_id, chat_id=chat_id).first()

async def on_star_payment_success(payment: SuccessfulPayment) -> None:
    async with in_transaction():
        start_payment_order = await StarPaymentOrder.get(
            invoice_id=payment.invoice_payload)
        if start_payment_order.status != RechargeStatusEnum.INIT:
            return
        await pay_success(str(start_payment_order.recharge_order_id), payment.invoice_payload, '')
        start_payment_order.status = RechargeStatusEnum.SUCCEED
        start_payment_order.final_amount = payment.total_amount
        start_payment_order.telegram_payment_charge_id = payment.telegram_payment_charge_id
        start_payment_order.provider_payment_charge_id = payment.provider_payment_charge_id
        start_payment_order.raw_data = payment.model_dump_json()
        await start_payment_order.save()

        user = await user_service.get_user_by_id(start_payment_order.user_id)
        recharge_order = await RechargeOrder.get(recharge_order_id=start_payment_order.recharge_order_id)
        await user_growth_service.add_fc_reward(user)
        await re_purchase_service.after_recharge(user, recharge_order)

async def star_compensate(starTransaction: StarTransaction) -> None:
    source: TransactionPartnerUser = starTransaction.source  # type: ignore
    async with in_transaction():
        start_payment_order = await StarPaymentOrder.get(
            invoice_id=source.invoice_payload)
        if start_payment_order.status != RechargeStatusEnum.INIT:
            return
        await pay_success(str(start_payment_order.recharge_order_id), 
                          str(start_payment_order.invoice_id), '')
        start_payment_order.status = RechargeStatusEnum.SUCCEED
        start_payment_order.final_amount = starTransaction.amount
        start_payment_order.telegram_payment_charge_id = starTransaction.id
        start_payment_order.provider_payment_charge_id = str(source.user.id)
        start_payment_order.raw_data = starTransaction.model_dump_json()
        await start_payment_order.save()

        user = await user_service.get_user_by_id(start_payment_order.user_id)
        recharge_order = await RechargeOrder.get(recharge_order_id=start_payment_order.recharge_order_id)
        await user_growth_service.add_fc_reward(user)
        await re_purchase_service.after_recharge(user, recharge_order)

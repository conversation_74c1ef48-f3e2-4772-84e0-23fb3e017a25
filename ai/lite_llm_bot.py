import asyncio
from datetime import datetime, timedelta
from hashlib import md5
import hashlib
import itertools
import logging
import os
from dotenv import load_dotenv
from litellm import (
    ChatCompletionAssistantMessage,
    ChatCompletionSystemMessage,
    ChatCompletionUserMessage,
    CustomStreamWrapper,
    acompletion,
)
from litellm.types.utils import ModelResponse

from ai.new_chat_bot import assemble_prompts_v3, record_log
from common.chat_bot_model import ChatStreamResponse
from common.common_constant import (
    Env,
    LlmModel,
    LlmRequestCluster,
)
from common.models.chat_model import ChatNextInput


from langchain_core.messages.base import BaseMessage
from services.chat import chat_cache_service
from utils import (
    env_util,
    exception_util,
    message_utils,
    preset_util,
    token_util,
)

load_dotenv()

log = logging.getLogger(__name__)
DEFAULT_MODEL = os.getenv("ai_model", "claude-3-haiku")
DEFAULT_BASE_URL = os.getenv("ai_base_url", "")
DEFAULT_LLM_KEY = os.getenv("litellm_key", "")

async def stream_call(input: ChatNextInput) -> ChatStreamResponse:
    input_messages: list[BaseMessage] = []
    input_messages = assemble_prompts_v3(input.preset_dict, input)
    input_messages = message_utils.process_merged(input_messages)
    stop = preset_util.find_stop_sequence(
        input.preset_dict, input.role_name, input.request_user_name
    )

    if (
        env_util.get_current_env() != Env.PROD
        and not input.isRetry
        and input.history
        and "ReturnException" in input.history[-1].content
    ):
        raise exception_util.param_error("Exception")
    token_message = [str(x.content) for x in input_messages]
    input_token = token_util.num_token_from_list(token_message)
    max_tokens = input.preset_dict["openai_max_tokens"]
    temperature = float(input.preset_dict["temperature"])
    top_p = input.preset_dict["top_p"]
    top_k = input.preset_dict["top_k"]
    frequency_penalty = input.preset_dict["frequency_penalty"]
    presence_penalty = input.preset_dict["presence_penalty"]
    extra_body = {
        "metadata": {
            "trace_user_id": input.user_id,
            "generation_name": "llm-call",
            "session_id": input.conversation_id,
            "trace_metadata": {"role_id": input.role_id},
        },
    }
    for key in input.llm_model_support_params:
        if key in input.preset_dict:
            extra_body[key] = input.preset_dict[key]
    # type: ignore
    record_log(input, token_message, input_token)
    input.timestamp_lite_req_start = int(datetime.now().timestamp())
    timeout = 10 if input.llm_request_cluster == LlmRequestCluster.FREE.value else 15
    request_messages, request_model = await chat_cache_service.cache_message(
        input_messages, input
    )
    if input.context_id:
        extra_body["context_id"] = input.context_id  # type: ignore
    # request_messages, request_model = await cache_message(input_messages, input)
    response = await acompletion(
        base_url=input.llm_request_base_url,
        timeout=timeout,
        api_key=DEFAULT_LLM_KEY,
        model=f"litellm_proxy/{request_model}",
        messages=request_messages,
        stream=True,
        max_tokens=max_tokens,
        temperature=temperature,
        top_p=top_p,
        frequency_penalty=frequency_penalty,
        presence_penalty=presence_penalty,
        stop=stop,
        top_k=top_k,
        extra_body=extra_body,
        max_retries=0,
        stream_options={"include_usage": True},
    )
    # response不是CustomStreamWrapper类型，直接返回
    if not isinstance(response, CustomStreamWrapper):
        raise exception_util.param_error(
            f"response is not CustomStreamWrapper, response type: {type(response)}"
        )
    headers = response._response_headers
    if headers and "x-litellm-call-id" in headers:
        input.llm_call_id = headers["x-litellm-call-id"]
    return ChatStreamResponse(
        success=True, error_type="", response=response, token_sum=input_token
    )


async def run_task(
    llm_model: str,
    system_message: str,
    user_message: str,
    ai_message: str = "",
    max_tokens: int = 200,
    base_url: str = DEFAULT_BASE_URL,
    metadata: dict = {},
) -> str:

    input_messages = [
        ChatCompletionSystemMessage(content=system_message, role="system"),
        ChatCompletionUserMessage(content=user_message, role="user"),
    ]
    if ai_message:
        input_messages.append(
            ChatCompletionAssistantMessage(content=ai_message, role="assistant")
        )
    extra_body = {}
    if metadata:
        extra_body["metadata"] = metadata
    response = await acompletion(
        base_url=base_url if base_url else DEFAULT_BASE_URL,
        timeout=60,
        api_key=DEFAULT_LLM_KEY,
        model=f"litellm_proxy/{llm_model}",
        messages=input_messages,
        max_tokens=max_tokens,
        extra_body=extra_body,
    )
    log.info("run task llm_model:%s, response:%s", llm_model, response)
    # response不是CustomStreamWrapper类型，直接返回
    if not isinstance(response, ModelResponse):
        raise exception_util.param_error(
            f"response is not CustomStreamWrapper, response type: {type(response)}"
        )
    if not response.choices or not response.choices[0].message:  # type: ignore
        return ""
    res = str(response.choices[0].message.content)  # type: ignore
    extra = response.model_extra
    log.info(f"llm_model:{llm_model}, response:{res}, extra:{extra}")
    return res


# async def req_ainvoke(input_messages: list[BaseMessage], request_model: str) -> str:

#     input_messages = transform_message(input_messages)
#     response = await acompletion(
#         base_url=DEFAULT_BASE_URL,
#         timeout=60,
#         api_key=DEFAULT_LLM_KEY,
#         model=f"litellm_proxy/{request_model}",
#         messages=input_messages,
#     )

#     # response不是CustomStreamWrapper类型，直接返回
#     if not isinstance(response, ModelResponse):
#         raise exception_util.param_error(
#             f"response is not CustomStreamWrapper, response type: {type(response)}"
#         )
#     if not response.choices or not response.choices[0].message: # type: ignore
#         return ""
#     return str(response.choices[0].message.content) # type: ignore


async def run_model_check_task(
    llm_model: str,
    system_message: str,
    user_message: str,
    max_tokens: int = 200,
    base_url: str = DEFAULT_BASE_URL,
) -> str:

    input_messages = [
        ChatCompletionSystemMessage(content=system_message, role="system"),
        ChatCompletionUserMessage(content=user_message, role="user"),
    ]
    response = await acompletion(
        base_url=base_url if base_url else DEFAULT_BASE_URL,
        timeout=60,
        api_key=DEFAULT_LLM_KEY,
        model=f"litellm_proxy/{llm_model}",
        messages=input_messages,
        max_tokens=max_tokens,
        enable_thinking=False,  # 禁用思考
        extra_body={
            "metadata": {
                "trace_user_id": "model_check",
                "generation_name": "llm-model-check",
                "session_id": "model_check_session",
            },
            "enable_thinking": False,  # 禁用思考
        },
    )
    log.info("run task llm_model:%s, response:%s", llm_model, response)
    # response不是CustomStreamWrapper类型，直接返回
    if not isinstance(response, ModelResponse):
        raise exception_util.param_error(
            f"response is not CustomStreamWrapper, response type: {type(response)}"
        )
    if not response.choices or not response.choices[0].message:  # type: ignore
        return ""
    res = str(response.choices[0].message.content)  # type: ignore
    if not res:
        res = response.choices[0].message.get("reasoning_content", "")  # type: ignore
    res = res if res else ""
    extra = response.model_extra
    log.info(f"llm_model:{llm_model}, response:{res}, extra:{extra}")
    return res


async def check_models_success(model: str, base_url: str = DEFAULT_BASE_URL) -> bool:
    system_message = "You are a helpful assistant."
    user_message = "Write a love sentence, no more than 100 words."
    for index in range(2):
        try:
            response = await run_model_check_task(
                model, system_message, user_message, 200, base_url=base_url
            )
        except Exception as e:
            log.error(f"CheckModelStatus Error exception, model: {model}, error: {e}")
            return False
        if not response or len(response) < 20:
            log.error(
                f"CheckModelStatus Error response_to_short: {model}({index}) response is empty"
            )
            return False
        log.info(f"CheckModelStatus Success: {model}({index}), response: {response}")
    return True
